# ThunderHub Core服务配置

# Core服务配置
core:
  id: "core-123.2"  # 服务ID，默认会使用主机名
  name: "雷电模拟器服务器"  # 服务显示名称
  service_name: "thunderhub-core"  # 服务名称，用于服务发现
  grpc_port: 50051    # gRPC服务端口
  rest_port: 8080     # REST服务端口
  file_server_host: "0.0.0.0"  # 文件服务器监听地址
  file_server_port: 8001       # 文件服务器端口

# 设备配置
devices:
  ldconsole_path: "H:\\MN\\leidian\\LDPlayer9\\ldconsole.exe"  # ldconsole.exe路径
  max_devices: 12     # 最大设备数量
  # 设备同步配置
  sync:
    interval: 7200    # 同步间隔从1小时增加到2小时

# 文件路径配置
files:
  paths:
    root_path: "H:\\PublishSystem"  # 文件根路径
    platform_path_template: "{root_path}\\{platform_id}"  # 平台路径模板
    device_path_template: "{platform_path}\\{device_name}"  # 设备路径模板
    content_path_template: "{device_path}\\content"  # 内容路径模板

# 服务配置
services:
  # Consul服务发现
  consul:
    host: "***************"
    port: 8500

  # Redis缓存
  redis:
    url: "redis://***************:6379/1"

  # 后端服务
  backend:
    url: "http://***************:8000"  # 后端服务URL

# 日志配置
logging:
  level: "INFO"       # 日志级别: DEBUG, INFO, WARNING, ERROR
  file: "core.log"    # 日志文件