import { createRouter, createWebHistory, type RouteLocationNormalized, type NavigationGuardNext } from 'vue-router'
import Login from '@/views/Login.vue'
import Layout from '@/components/Layout.vue'
import { useAuthStore } from '@/stores/auth'

declare module 'vue-router' {
  interface RouteMeta {
    requiresAuth: boolean
    menuItem?: boolean // 是否显示在菜单中
    title?: string // 菜单显示标题
    icon?: string // 菜单图标
  }
}

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      // 设备管理
      {
        path: 'devices',
        name: 'Devices',
        component: () => import('@/views/device/DeviceList.vue'),
        meta: {
          requiresAuth: true,
          menuItem: true,
          title: '设备管理',
          icon: 'Monitor'
        }
      },
      {
        path: 'devices/list',
        name: 'DeviceList',
        component: () => import('@/views/device/DeviceList.vue'),
        meta: {
          requiresAuth: true,
          menuItem: true,
          title: '设备设置',
          icon: 'Cpu'
        }
      },
      {
        path: 'devices/control',
        name: 'DeviceControl',
        component: () => import('@/views/device/Control.vue'),
        meta: {
          requiresAuth: true,
          menuItem: true,
          title: '设备控制',
          icon: 'List'
        }
      },
      {
        path: 'devices/proxy',
        name: 'ProxyManagement',
        component: () => import('@/views/device/ProxyManagement.vue'),
        meta: {
          requiresAuth: true,
          menuItem: true,
          title: 'IP管理',
          icon: 'Connection'
        }
      },

      // 任务调度
      {
        path: 'tasks',
        name: 'TaskScheduler',
        component: () => import('@/views/task/Management.vue'),
        meta: {
          requiresAuth: true,
          menuItem: true,
          title: '任务调度',
          icon: 'Clock'
        }
      },
      {
        path: 'tasks/management',
        name: 'TaskManagement',
        component: () => import('@/views/task/Management.vue'),
        meta: {
          requiresAuth: true,
          menuItem: true,
          title: '任务管理',
          icon: 'Setting'
        }
      },
      {
        path: 'tasks/scheduler',
        name: 'LongTermTaskScheduler',
        component: () => import('@/views/TaskScheduler.vue'),
        meta: {
          requiresAuth: true,
          menuItem: true,
          title: '长期任务',
          icon: 'Timer'
        }
      },
      {
        path: 'tasks/history',
        name: 'TaskHistory',
        component: () => import('@/views/task/History.vue'),
        meta: {
          requiresAuth: true,
          menuItem: true,
          title: '执行历史',
          icon: 'Document'
        }
      },

      // 报表中心
      {
        path: 'reports',
        name: 'ReportCenter',
        component: () => import('@/views/report/Center.vue'),
        meta: {
          requiresAuth: true,
          menuItem: true,
          title: '数据报表',
          icon: 'DataAnalysis'
        }
      },

      // 社媒管理
      {
        path: 'social',
        name: 'SocialMedia',
        component: () => import('@/views/social/Media.vue'),
        meta: {
          requiresAuth: true,
          menuItem: true,
          title: '社媒管理',
          icon: 'ChatRound'
        },
        children: [
          {
            path: 'accounts',
            name: 'SocialAccounts',
            component: () => import('@/views/social/AccountManagement.vue'),
            meta: {
              requiresAuth: true,
              menuItem: true,
              title: '账号管理',
              icon: 'User'
            }
          },
          {
            path: 'benchmark',
            name: 'BenchmarkAccounts',
            component: () => import('@/views/social/BenchmarkAccountsNew.vue'),
            meta: {
              requiresAuth: true,
              menuItem: true,
              title: '对标账号',
              icon: 'TrendCharts'
            }
          },
          {
            path: 'posts',
            name: 'SocialPosts',
            component: () => import('@/views/social/components/PostManagement.vue'),
            meta: {
              requiresAuth: true,
              menuItem: true,
              title: '内容管理',
              icon: 'Document'
            }
          },
          {
            path: 'analytics',
            name: 'SocialAnalytics',
            component: () => import('@/views/social/components/AnalyticsDashboard.vue'),
            meta: {
              requiresAuth: true,
              menuItem: true,
              title: '数据分析',
              icon: 'DataAnalysis'
            }
          },
          {
            path: 'publish',
            name: 'PublishManagement',
            component: () => import('@/views/social/PublishManagement.vue'),
            meta: {
              requiresAuth: true,
              menuItem: true,
              title: '发布管理',
              icon: 'Upload'
            }
          },
        ]
      },

      // 文件中心
      {
        path: 'docs',
        name: 'DocumentCenter',
        component: () => import('@/views/doc/Index.vue'),
        meta: {
          requiresAuth: true,
          menuItem: true,
          title: '文件中心',
          icon: 'Folder'
        },
        children: [
          {
            path: '',
            redirect: { name: 'FileManager' }
          },
          {
            path: 'files',
            name: 'FileManager',
            component: () => import('@/views/doc/Manager.vue'),
            meta: {
              requiresAuth: true,
              menuItem: true,
              title: '文件管理',
              icon: 'Document'
            }
          },
          {
            path: 'benchmark-download',
            name: 'BenchmarkDownload',
            component: () => import('@/views/doc/BenchmarkDownload.vue'),
            meta: {
              requiresAuth: true,
              menuItem: true,
              title: '对标下载',
              icon: 'Download'
            }
          },
          {
            path: 'content-collect',
            name: 'ContentCollect',
            component: () => import('@/views/doc/ContentCollect.vue'),
            meta: {
              requiresAuth: true,
              menuItem: true,
              title: '内容采集',
              icon: 'Collection'
            }
          },
          {
            path: 'content-generate',
            name: 'ContentGenerate',
            component: () => import('@/views/doc/ContentGenerate.vue'),
            meta: {
              requiresAuth: true,
              menuItem: true,
              title: '内容生成',
              icon: 'MagicStick'
            }
          }
        ]
      },

      // 默认重定向
      {
        path: '',
        redirect: { name: 'DeviceList' }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: { name: 'Login' }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

router.beforeEach(async (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) => {
  const authStore = useAuthStore()

  // 初始化时强制检查认证状态
  await authStore.checkAuth()

  console.log(`Routing from ${from.name?.toString()} to ${to.name?.toString()}`)

  try {
    // 跳过登录页的认证检查
    if (to.name === 'Login') {
      // 如果已登录，直接跳转到首页
      if (authStore.isAuthenticated) {
        next({ name: 'DeviceList' })
      } else {
        next()
      }
      return
    }

    // 其他页面根据路由元信息决定是否检查认证
    if (to.meta.requiresAuth !== false) {
      const isAuthenticated = await authStore.checkAuth()
      if (!isAuthenticated) {
        next({ name: 'Login', query: { redirect: to.fullPath } })
        return
      }
    }

    console.log('Proceeding to route')
    next()
  } catch (error) {
    console.error('Router error:', error)
    next({ name: 'Login' })
  }
})

export default router


