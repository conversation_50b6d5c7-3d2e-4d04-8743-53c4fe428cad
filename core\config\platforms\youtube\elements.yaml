# YouTube平台元素定位配置
# 每个元素支持多种定位方法，按优先级排序

elements:
  # 创建按钮（+按钮）
  create_button:
    - type: "accessibility_id"
      value: "创作"
      timeout: 10
      description: "通过无障碍ID定位创作按钮"
    - type: "id"
      value: "com.google.android.youtube:id/image"
      timeout: 5
      description: "通过resource-id定位创建按钮"
    - type: "id"
      value: "com.google.android.youtube:id/fab"
      timeout: 5
      description: "通过fab resource-id定位"
    - type: "xpath"
      value: "//android.widget.Button[@content-desc='创作']"
      timeout: 10
      description: "通过xpath定位创作按钮"
    - type: "xpath"
      value: "//*[@content-desc='创作']"
      timeout: 10
      description: "通用xpath定位创作按钮"

  # 短视频选项
  shorts_option:
    - type: "xpath"
      value: "//*[@text='短视频']"
      timeout: 10
      description: "通用xpath定位短视频选项"
    - type: "xpath"
      value: "//android.widget.TextView[@text='短视频']"
      timeout: 10
      description: "通过文本定位短视频选项"    
    - type: "xpath"
      value: "//android.widget.TextView[contains(@text, '短视频')]"
      timeout: 10
      description: "模糊匹配短视频选项"

  # 普通视频上传选项
  video_upload_option:
    - type: "xpath"
      value: "//android.widget.TextView[@text='上传视频']"
      timeout: 10
      description: "通过文本定位上传视频选项"

  # 草稿对话框 - 重新开始按钮
  draft_restart_button:
    - type: "xpath"
      value: "//android.widget.Button[@text='重新开始']"
      timeout: 5
      description: "通过文本定位重新开始按钮"
    - type: "id"
      value: "android:id/button2"
      timeout: 5
      description: "通过resource-id定位重新开始按钮"
    - type: "xpath"
      value: "//*[@text='重新开始']"
      timeout: 5
      description: "通用xpath定位重新开始按钮"
    - type: "xpath"
      value: "//android.widget.Button[contains(@text, '重新')]"
      timeout: 5
      description: "模糊匹配重新开始按钮"

  # 草稿对话框 - 删除草稿按钮
  draft_delete_button:
    - type: "xpath"
      value: "//android.widget.Button[@text='删除草稿']"
      timeout: 5
      description: "通过文本定位删除草稿按钮"
    - type: "xpath"
      value: "//android.widget.Button[@text='删除']"
      timeout: 5
      description: "通过文本定位删除按钮"
    - type: "id"
      value: "android:id/button1"
      timeout: 5
      description: "通过resource-id定位删除按钮"

  # 导入照片库中的视频按钮
  gallery_button:
    - type: "xpath"
      value: "//*[@content-desc='导入照片库中的视频']"
      timeout: 10
      description: "通用xpath定位导入照片库按钮"
    - type: "id"
      value: "com.google.android.youtube:id/reel_camera_gallery_button_delegate"
      timeout: 15
      description: "通过resource-id定位导入照片库按钮（可点击的FrameLayout）"
    - type: "xpath"
      value: "//android.widget.FrameLayout[@content-desc='导入照片库中的视频']"
      timeout: 10
      description: "通过content-desc定位导入照片库按钮"
    - type: "xpath"
      value: "//*[contains(@content-desc, '导入照片库')]"
      timeout: 10
      description: "模糊匹配导入照片库按钮"

  # 视频文件选择（动态内容）
  video_file:
    - type: "xpath_template"
      value: "//android.widget.ImageView[@content-desc=\"{filename}\"]"
      timeout: 10
      description: "精确匹配：通过完整文件名content-desc定位视频文件（使用双引号）"
    - type: "xpath_template"
      value: "//*[@content-desc=\"{filename}\"]"
      timeout: 10
      description: "通用匹配：通过文件名匹配任意元素类型（使用双引号）"
    - type: "xpath_template"
      value: "//android.widget.ImageView[@content-desc=\"{filename_clean}\"]"
      timeout: 10
      description: "备用匹配：通过清理后的文件名content-desc定位视频文件"
    - type: "xpath_template"
      value: "//*[@content-desc=\"{filename_clean}\"]"
      timeout: 10
      description: "备用通用匹配：通过清理后的文件名匹配任意元素类型"
    - type: "xpath_template"
      value: "//android.widget.ImageView[contains(@content-desc, \"{filename}\")]"
      timeout: 10
      description: "模糊匹配：通过文件名部分匹配content-desc定位视频文件"
    - type: "xpath_template"
      value: "//android.widget.ImageView[contains(@content-desc, \"{filename_clean}\")]"
      timeout: 10
      description: "备用模糊匹配：通过清理后的文件名部分匹配"
    - type: "xpath_template"
      value: "//*[contains(@content-desc, \"{filename}\") and contains(@content-desc, \".mp4\")]"
      timeout: 10
      description: "安全匹配：确保包含文件名且是mp4文件"
    - type: "xpath"
      value: "//android.widget.ImageView[contains(@content-desc, '.mp4')]"
      timeout: 8
      description: "备用方案：通过文件扩展名定位视频文件"
    - type: "xpath"
      value: "//android.widget.ImageView[@clickable='true']"
      timeout: 5
      description: "最后备用：通过可点击属性定位视频文件"

  # 完成编辑按钮
  finish_editing_button:
    - type: "xpath"
      value: "//android.widget.Button[@content-desc='将片段添加至项目']"
      timeout: 10
      description: "通过content-desc定位完成按钮"
    - type: "xpath"
      value: "//*[@text='完成']"
      timeout: 10
      description: "通用xpath定位完成按钮"
    - type: "id"
      value: "com.google.android.youtube:id/shorts_trim_finish_trim_button"
      timeout: 15
      description: "通过resource-id定位完成按钮（方法1）"
    - type: "xpath"
      value: "//android.widget.Button[@text='完成']"
      timeout: 10
      description: "通过文本定位完成按钮"
    - type: "coordinate"
      value: [800, 650]
      timeout: 10
      description: "通过坐标定位完成按钮（备用方案）"

  # 添加音效按钮
  add_music_button:
    - type: "xpath"
      value: "//android.widget.Button[@resource-id='com.google.android.youtube:id/shorts_camera_music_button' and @clickable='true']"
      timeout: 10
      description: "通过Button类型、resource-id和clickable属性定位添加音效按钮（最准确）"
    - type: "xpath"
      value: "//android.widget.Button[@bounds='[334,120][566,200]' and @clickable='true']"
      timeout: 10
      description: "通过精确bounds和clickable属性定位添加音效按钮"
    - type: "coordinate"
      value: [450, 160]
      timeout: 5
      description: "通过坐标点击添加音效按钮（根据bounds [334,120][566,200]的中心点）"
    - type: "xpath"
      value: "//*[@content-desc='添加音效，适用于时长不超过 60 秒的视频。']"
      timeout: 10
      description: "通过完整content-desc定位添加音效按钮"
    - type: "id"
      value: "com.google.android.youtube:id/shorts_camera_music_button"
      timeout: 10
      description: "通过resource-id定位添加音效按钮（备用）"

  # 音乐搜索框
  music_search_box:
    - type: "id"
      value: "com.google.android.youtube:id/music_picker_search_box"
      timeout: 10
      description: "通过resource-id定位音乐搜索框"
 # 已保存音乐标签页
  saved_music_tab:
    - type: "xpath"
      value: "//*[@text='已保存']"
      timeout: 10
      description: "通用xpath定位已保存按钮"
    - type: "xpath"
      value: "//android.widget.TextView[@resource-id='com.google.android.youtube:id/text' and @text='已保存']"
      timeout: 10
      description: "通过content-desc定位已保存标签页按钮"    
    - type: "id"
      value: "com.google.android.youtube:id/text"
      timeout: 10
      description: "通过resource-id定位标签页按钮（可能匹配多个，需要进一步筛选）"



  # 音乐搜索输入框
  music_search_input:
    - type: "xpath"
      value: "//android.widget.EditText[@resource-id='com.google.android.youtube:id/music_picker_search_box']"
      timeout: 10
      description: "通过xpath定位音乐搜索输入框"
    - type: "xpath"
      value: "//android.widget.EditText[contains(@hint, '搜索')]"
      timeout: 5
      description: "通过hint文本定位搜索输入框"

  # 音乐列表容器
  music_list:
    - type: "id"
      value: "com.google.android.youtube:id/section_list"
      timeout: 10
      description: "通过resource-id定位音乐列表容器"
    - type: "xpath"
      value: "//android.support.v7.widget.RecyclerView[@resource-id='com.google.android.youtube:id/section_list']"
      timeout: 10
      description: "通过xpath定位音乐列表RecyclerView"

  # 音乐选择项（模板）
  music_item:  
    - type: "xpath"
      value: "//android.view.ViewGroup[@clickable='true' and contains(@content-desc, 'Shorts')]"
      timeout: 5
      description: "通过Shorts关键词定位任意音乐项"
    - type: "xpath_template"
      value: "//android.view.ViewGroup[contains(@content-desc, '{music_title}') and @clickable='true']"
      timeout: 10
      description: "通过音乐标题定位音乐项"
  # 添加音乐到视频按钮
  add_music_to_video_button:  
    - type: "coordinate"
      value: [828, 422]
      description: "通过坐标点击添加音乐按钮（根据bounds [788,382][868,462]的中心点）"
    - type: "xpath"
      value: "//android.view.ViewGroup[@content-desc='将此音乐添加到你的视频中']"
      timeout: 10
      description: "通过content-desc定位添加音乐按钮"
  # 音乐选择器关闭按钮
  music_picker_close_button:
    - type: "id"
      value: "com.google.android.youtube:id/music_picker_leading_button"
      timeout: 5
      description: "通过resource-id定位音乐选择器关闭按钮"
    - type: "xpath"
      value: "//android.widget.ImageView[@content-desc='关闭']"
      timeout: 5
      description: "通过content-desc定位关闭按钮"
  # 前往编辑器按钮（选择音乐后的下一步）
  go_to_editor_button:
    - type: "id"
      value: "com.google.android.youtube:id/shorts_camera_next_button_delegate"
      timeout: 10
      description: "通过resource-id定位前往编辑器按钮"
    - type: "xpath"
      value: "//android.widget.Button[@content-desc='前往编辑器']"
      timeout: 10
      description: "通过content-desc定位前往编辑器按钮"

  # 音量调节按钮（正确的音频设置按钮）
  volume_settings_button:
    - type: "id"
      value: "com.google.android.youtube:id/shorts_edit_volume_button"
      timeout: 10
      description: "通过resource-id定位音量调节按钮"
    - type: "xpath"
      value: "//android.widget.FrameLayout[@content-desc='音量']"
      timeout: 10
      description: "通过content-desc定位音量按钮"
    - type: "coordinate"
      value: [836, 118]
      description: "通过坐标点击音量按钮（根据bounds [772,60][900,176]的中心点）"

  # 编辑器工具按钮（已弃用，使用volume_settings_button）
  editor_tool_button:
    - type: "id"
      value: "com.google.android.youtube:id/shorts_edit_volume_button"
      timeout: 10
      description: "重定向到正确的音量按钮"
    - type: "xpath"
      value: "//android.widget.FrameLayout[@content-desc='音量']"
      timeout: 10
      description: "通过content-desc定位音量按钮"

  # 音乐音量调节滑块（背景音乐）
  music_volume_slider:
    - type: "xpath"
      value: "//android.widget.SeekBar[@resource-id='com.google.android.youtube:id/slider' and @bounds='[16,1476][884,1572]']"
      timeout: 10
      description: "通过xpath和bounds定位背景音乐音量滑块"
    - type: "coordinate"
      value: [450, 1524]
      description: "通过坐标定位背景音乐滑块中心点（根据bounds [16,1476][884,1572]的中心点）"

  # 原声音量调节滑块
  original_audio_volume_slider:
    - type: "xpath"
      value: "//android.widget.SeekBar[@resource-id='com.google.android.youtube:id/slider' and @bounds='[16,1300][884,1396]']"
      timeout: 10
      description: "通过xpath和bounds定位原声音量滑块"
    - type: "coordinate"
      value: [450, 1348]
      description: "通过坐标定位原声音量滑块中心点（根据bounds [16,1300][884,1396]的中心点）"

  # 通用音量滑块（当无法区分具体滑块时使用）
  volume_slider_generic:
    - type: "id"
      value: "com.google.android.youtube:id/slider"
      timeout: 10
      description: "通过resource-id定位通用音量滑块"

  # 音频设置完成按钮
  audio_settings_done_button:
    - type: "id"
      value: "com.google.android.youtube:id/button_done"
      timeout: 10
      description: "通过resource-id定位音频设置完成按钮"
    - type: "xpath"
      value: "//android.widget.ImageView[@content-desc='完成']"
      timeout: 10
      description: "通过content-desc定位完成按钮"

  # 下一步按钮（编辑器页面）
  editor_next_button:
    - type: "id"
      value: "com.google.android.youtube:id/shorts_post_bottom_button"
      timeout: 10
      description: "通过resource-id定位编辑器下一步按钮"
    - type: "xpath"
      value: "//android.widget.Button[@text='下一步']"
      timeout: 10
      description: "通过文本定位下一步按钮"
  # 标题输入框（YouTube Shorts）
  title_input:
    - type: "xpath"
      value: "//android.view.View[@text='为你的短视频添加标题']"
      timeout: 10
      description: "通过text定位Shorts标题输入框"
    - type: "xpath"
      value: "//android.view.View[@hint='为你的短视频添加标题']"
      timeout: 10
      description: "通过hint定位Shorts标题输入框"
    - type: "xpath"
      value: "//*[@text='为你的短视频添加标题']"
      timeout: 10
      description: "通用xpath定位标题输入框"
    - type: "xpath"
      value: "//*[@hint='为你的短视频添加标题']"
      timeout: 10
      description: "通用xpath定位标题输入框（hint）"
    - type: "xpath"
      value: "//*[contains(@text, '添加标题')]"
      timeout: 10
      description: "模糊匹配标题输入框"
    - type: "xpath"
      value: "//*[contains(@hint, '添加标题')]"
      timeout: 10
      description: "模糊匹配标题输入框（hint）"
    - type: "xpath"
      value: "//android.widget.EditText"
      timeout: 10
      description: "查找EditText元素"
    - type: "id"
      value: "com.google.android.youtube:id/title_edit"
      timeout: 10
      description: "传统标题输入框（备用）"

  # 描述输入框
  description_input:
    - type: "id"
      value: "com.google.android.youtube:id/description_edit"
      timeout: 10
      description: "描述输入框"

  # 隐私设置按钮（YouTube Shorts）
  privacy_button:
    - type: "xpath"
      value: "//android.view.ViewGroup[@bounds='[0,713][900,829]' and @clickable='true']"
      timeout: 10
      description: "通过精确bounds和clickable属性定位隐私设置按钮（最准确）"
    - type: "xpath"
      value: "//android.view.ViewGroup[@clickable='true' and @index='2']"
      timeout: 10
      description: "通过clickable和index属性定位隐私设置按钮"
    - type: "coordinate"
      value: [450, 771]
      timeout: 5
      description: "通过坐标点击隐私设置按钮（根据bounds [0,713][900,829]的中心点）"
    - type: "id"
      value: "com.google.android.youtube:id/privacy_spinner"
      timeout: 10
      description: "通过resource-id定位隐私设置按钮（备用）"
    - type: "xpath"
      value: "//android.support.v7.widget.RecyclerView[@resource-id='com.google.android.youtube:id/recycler_view']//*[@clickable='true' and @index='2']"
      timeout: 10
      description: "在RecyclerView中查找index为2的可点击元素"
    - type: "xpath"
      value: "//android.support.v7.widget.RecyclerView[@resource-id='com.google.android.youtube:id/recycler_view']/android.view.ViewGroup/android.view.ViewGroup[3]"
      timeout: 10
      description: "原始xpath定位（备用）"

  # 隐私选项（YouTube Shorts）
  privacy_option_public:
    - type: "xpath"
      value: "//android.widget.ScrollView/android.view.ViewGroup/android.view.ViewGroup[2]/android.view.ViewGroup[1]"
      timeout: 10
      description: "通过结构化路径定位公开选项（备用）"
    - type: "xpath"
      value: "//android.view.ViewGroup[@bounds='[0,272][900,416]' and @clickable='true' and @index='0']"
      timeout: 10
      description: "通过精确bounds、clickable和index属性定位公开选项（最准确）"
    - type: "coordinate"
      value: [450, 344]
      timeout: 5
      description: "通过坐标点击公开选项（根据bounds [0,272][900,416]的中心点）"
    - type: "xpath"
      value: "//android.widget.TextView[@text='公开']"
      timeout: 10
      description: "通过文本定位公开选项"
    - type: "xpath"
      value: "//*[@text='公开']"
      timeout: 10
      description: "通用xpath定位公开选项（备用）"


  privacy_option_unlisted:
    - type: "xpath"
      value: "//android.widget.ScrollView/android.view.ViewGroup/android.view.ViewGroup[2]/android.view.ViewGroup[2]"
      timeout: 10
      description: "通过结构化路径定位不公开选项（备用）"
    - type: "xpath"
      value: "//android.view.ViewGroup[@bounds='[0,418][900,562]' and @clickable='true' and @index='1']"
      timeout: 10
      description: "通过精确bounds、clickable和index属性定位不公开选项（最准确）"
    - type: "coordinate"
      value: [450, 490]
      timeout: 5
      description: "通过坐标点击不公开选项（根据bounds [0,418][900,562]的中心点）"
    - type: "xpath"
      value: "//android.view.ViewGroup[@clickable='true' and @index='1']"
      timeout: 10
      description: "通过clickable和index属性定位不公开选项"
    - type: "xpath"
      value: "//android.widget.TextView[@text='不公开']"
      timeout: 10
      description: "通过文本定位不公开选项（备用）"

  privacy_option_private:
    - type: "xpath"
      value: "//android.widget.ScrollView/android.view.ViewGroup/android.view.ViewGroup[2]/android.view.ViewGroup[3]"
      timeout: 10
      description: "通过结构化路径定位公开选项（备用）"
    - type: "xpath"
      value: "//android.view.ViewGroup[@bounds='[0,564][900,708]' and @clickable='true' and @index='2']"
      timeout: 10
      description: "通过精确bounds、clickable和index属性定位私享选项（最准确）"
    - type: "coordinate"
      value: [450, 636]
      timeout: 5
      description: "通过坐标点击私享选项（根据bounds [0,564][900,708]的中心点）"
    - type: "xpath"
      value: "//android.view.ViewGroup[@clickable='true' and @index='2']"
      timeout: 10
      description: "通过clickable和index属性定位私享选项"
    - type: "xpath"
      value: "//android.widget.TextView[@text='私享']"
      timeout: 10
      description: "通过文本定位私享选项（备用）"

  
  # 预定时间发布切换按钮
  schedule_toggle_button:
    - type: "xpath"
      value: "//*[contains(@content-desc, '预定')]"
      timeout: 10
      description: "通过content-desc模糊匹配预定时间发布切换按钮"
    - type: "xpath"
      value: "//*[contains(@text, '预定')]"
      timeout: 10
      description: "通过文本模糊匹配预定时间发布切换按钮"
    - type: "xpath"
      value: "//*[contains(@content-desc, '时间')]"
      timeout: 10
      description: "通过时间关键词匹配预定发布按钮"

  # 发布时间设置区域
  publish_time_setting:
    - type: "xpath"
      value: "//android.view.ViewGroup[contains(@content-desc, '年') and contains(@content-desc, '月') and contains(@content-desc, '日')]"
      timeout: 10
      description: "通过content-desc模式匹配发布时间设置区域"
    - type: "xpath"
      value: "//*[contains(@content-desc, '年') and contains(@content-desc, '月')]"
      timeout: 10
      description: "通用xpath匹配发布时间设置区域"
    - type: "xpath"
      value: "//*[contains(@text, '年') and contains(@text, '月')]"
      timeout: 10
      description: "通过文本匹配发布时间设置区域"

  # 返回主界面按钮
  back_to_main_button:
    - type: "xpath"
      value: "//android.widget.ImageView[@content-desc='返回']"
      timeout: 10
      description: "通过content-desc定位返回按钮"
    - type: "xpath"
      value: "//*[@content-desc='返回']"
      timeout: 10
      description: "通用xpath定位返回按钮"
    - type: "xpath"
      value: "//android.widget.ImageView[@content-desc='向上导航']"
      timeout: 10
      description: "通过content-desc定位向上导航按钮"
    - type: "coordinate"
      value: [64, 96]
      timeout: 10
      description: "通过坐标定位返回按钮（bounds [40,72][88,120]的中心点）"
    - type: "xpath"
      value: "//android.widget.ImageView[@bounds='[40,72][88,120]']"
      timeout: 10
      description: "通过bounds属性定位返回按钮"
    - type: "xpath"
      value: "//*[@content-desc='向上导航']"
      timeout: 10
      description: "通用xpath定位向上导航按钮"

  # 最终上传按钮（YouTube Shorts）
  final_upload_button:
    - type: "id"
      value: "com.google.android.youtube:id/upload_bottom_button"
      timeout: 10
      description: "最终上传短视频按钮"
    - type: "xpath"
      value: "//android.widget.Button[@text='上传短视频']"
      timeout: 10
      description: "通过文本定位上传短视频按钮"
    - type: "xpath"
      value: "//android.widget.Button[@resource-id='com.google.android.youtube:id/upload_bottom_button']"
      timeout: 10
      description: "通过resource-id定位上传按钮"
    - type: "xpath"
      value: "//*[@text='上传短视频']"
      timeout: 10
      description: "通用xpath定位上传短视频按钮"
    - type: "xpath"
      value: "//*[contains(@text, '上传')]"
      timeout: 10
      description: "模糊匹配上传按钮"

  # 上传按钮（传统/备用）
  upload_button:
    - type: "id"
      value: "com.google.android.youtube:id/upload_button"
      timeout: 30      # 超时时间从10秒增加到30秒
      description: "传统上传按钮"
    - type: "xpath"
      value: "//*[@text='上传']"
      timeout: 30      # 超时时间从10秒增加到30秒
      description: "通过文本定位上传按钮"


# 等待时间配置
wait_times:
  after_click: 5             # 点击后等待从2秒增加到5秒
  after_app_launch: 15       # 应用启动后等待从5秒增加到15秒
  after_shorts_selection: 8  # Shorts选择后等待从3秒增加到8秒
  after_video_selection: 6   # 视频选择后等待从2秒增加到6秒
  after_editing: 20          # 编辑完成后等待从8秒增加到20秒
  before_upload: 3           # 上传前等待从1秒增加到3秒
  video_processing: 30       # 视频处理专用等待时间从10秒增加到30秒

# 重试配置
retry_config:
  max_retries: 5             # 最大重试次数从3次增加到5次
  retry_delay: 3             # 重试延迟从1秒增加到3秒
  timeout_multiplier: 2.0    # 超时倍数从1.5增加到2.0
