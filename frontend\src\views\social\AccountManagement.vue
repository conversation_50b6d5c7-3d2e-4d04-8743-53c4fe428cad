<template>
  <div class="account-management">
    <h1 class="page-title">账号管理</h1>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <el-form :inline="true" class="filter-form">
        <el-form-item label="平台">
          <div class="platform-select-container">
            <el-select v-model="filters.platformId" placeholder="选择平台" clearable popper-class="wider-dropdown">
              <el-option
                v-for="platform in platforms"
                :key="platform.id"
                :label="platform.name"
                :value="platform.id === 'youtube' ? 'youtube' : platform.id"
              >
                <div class="platform-option">
                  <img
                    v-if="platform.icon"
                    :src="getPlatformIcon(platform.id)"
                    class="platform-icon"
                    alt="平台图标"
                  />
                  <span>{{ platform.name }}</span>
                </div>
              </el-option>
            </el-select>
            <el-button type="primary" link @click="showPlatformManagementDialog">
              管理平台
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="Core服务">
          <el-select v-model="filters.coreServiceId" placeholder="选择Core服务" clearable popper-class="wider-dropdown">
            <el-option
              v-for="service in coreServices"
              :key="service.id"
              :label="service.name"
              :value="service.id"
            >
              <div class="service-option">
                <span>{{ service.name }}</span>
                <span class="service-info" v-if="service.host">{{ service.host }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable popper-class="wider-dropdown">
            <el-option label="活跃" value="active">
              <div class="status-option">
                <el-tag type="success">活跃</el-tag>
              </div>
            </el-option>
            <el-option label="非活跃" value="inactive">
              <div class="status-option">
                <el-tag type="info">非活跃</el-tag>
              </div>
            </el-option>
            <el-option label="已暂停" value="suspended">
              <div class="status-option">
                <el-tag type="warning">已暂停</el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-input
            v-model="filters.keyword"
            placeholder="搜索账号（支持用户名、显示名称、备注等）"
            clearable
            @clear="handleSearch"
            @input="handleSearchInput"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作区域 -->
    <div class="action-section">
      <div class="action-buttons">
        <el-button type="primary" @click="showAddAccountDialog">添加账号</el-button>
        <el-button type="success" @click="showImportDialog">导入账号</el-button>
        <el-button type="info" @click="handleExport">导出账号</el-button>
        <el-button
          type="danger"
          :disabled="selectedAccountIds.length === 0"
          @click="handleBatchDelete"
        >
          批量删除
        </el-button>
        <el-button
          type="warning"
          :disabled="selectedAccountIds.length === 0"
          @click="showBatchUpdateDialog"
        >
          批量更新
        </el-button>

        <!-- 快速排序按钮 -->
        <el-button-group class="sort-buttons">
          <el-button size="small" @click="quickSortByExpiry">
            按过期状态排序
          </el-button>
          <el-button size="small" @click="quickSortByUpdateDate">
            按更新日期排序
          </el-button>
        </el-button-group>
      </div>

      <!-- 分页控件 -->
      <div class="action-pagination" v-if="pagination.showPagination">
        <el-pagination
          :current-page="pagination.page"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 账号列表 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="accounts"
        style="width: 100%"
        max-height="calc(100vh - 350px)"
        @selection-change="handleSelectionChange"
        :row-class-name="getRowClassName"
      >
      <el-table-column type="selection" width="55" />

      <el-table-column label="账号信息" min-width="200">
        <template #default="scope">
          <div class="account-info">
            <div class="account-main-info">
              <span class="account-display-name">{{ getAccountDisplayName(scope.row) }}</span>
              <el-tag v-if="scope.row.tags && scope.row.tags.length > 0" size="small" type="primary" class="primary-tag">
                {{ scope.row.tags[0] }}
              </el-tag>
            </div>
            <div class="account-username" v-if="scope.row.username">{{ scope.row.username }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="平台" min-width="100">
        <template #default="scope">
          <div class="platform-cell">
            <img
              v-if="getPlatformIcon(scope.row.platform_id)"
              :src="getPlatformIcon(scope.row.platform_id)"
              class="platform-icon"
              alt="平台图标"
            />
            <span>{{ getPlatformName(scope.row.platform_id) }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="关联设备" min-width="200">
        <template #default="scope">
          <div v-if="scope.row.device_name || scope.row.linked_device_id" class="device-link-info">
            <el-tag type="success" size="small" class="device-link-tag">
              <i class="el-icon-link"></i> 已关联
            </el-tag>
            <span class="device-name">{{ scope.row.device_name || getDeviceName(scope.row.linked_device_id) }}</span>
            <div class="device-actions">
              <el-button type="primary" link size="small" @click="showLinkDeviceDialog(scope.row)">
                管理
              </el-button>
              <el-button
                type="success"
                link
                size="small"
                @click="handleOpenDevice(scope.row)"
                :loading="deviceOpeningStatus[scope.row.id]"
              >
                打开设备
              </el-button>
            </div>
          </div>
          <div v-else class="device-link-info">
            <el-tag type="info" size="small" class="device-link-tag">
              <i class="el-icon-disconnect"></i> 未关联
            </el-tag>
            <el-button type="primary" size="small" @click="showLinkDeviceDialog(scope.row)">
              关联设备
            </el-button>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="Core服务" min-width="120">
        <template #default="scope">
          {{ getCoreServiceName(scope.row.core_service_id) }}
        </template>
      </el-table-column>

      <el-table-column label="所有标签" min-width="120">
        <template #default="scope">
          <div v-if="scope.row.tags && scope.row.tags.length > 1" class="tags-container">
            <el-tag
              v-for="(tag, index) in scope.row.tags.slice(1)"
              :key="index"
              size="small"
              class="account-tag"
            >
              {{ tag }}
            </el-tag>
          </div>
          <span v-else-if="!scope.row.tags || scope.row.tags.length === 0" class="no-tags">无标签</span>
          <span v-else class="single-tag">仅一个标签</span>
        </template>
      </el-table-column>

      <el-table-column label="更新日期" min-width="120" sortable :sort-method="sortByUpdateDate">
        <template #default="scope">
          {{ formatExpiryDate(scope.row.last_updated_date) }}
        </template>
      </el-table-column>

      <el-table-column label="过期状态" min-width="120" sortable :sort-method="sortByExpiryStatus">
        <template #default="scope">
          <span
            v-if="scope.row.last_updated_date"
            :class="getExpiryStatusTextClass(scope.row.last_updated_date)"
          >
            {{ getExpiryInfo(scope.row.last_updated_date).message }}
          </span>
          <span v-else class="no-expiry">未设置</span>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" min-width="160">
        <template #default="scope">
          {{ formatDate(scope.row.created_at) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="280" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button
            size="small"
            type="success"
            @click="handleCopy(scope.row.id)"
          >
            复制
          </el-button>
          <el-button
            size="small"
            type="danger"
            @click="handleDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>

      <!-- 表格底部插槽，已移除分页组件 -->
      <!-- <template #append>
      </template> -->
    </el-table>
    </div>

    <!-- 添加/编辑账号对话框 -->
    <account-form-dialog
      v-if="accountFormDialog.visible"
      v-model:visible="accountFormDialog.visible"
      :title="accountFormDialog.title"
      :form-data="accountFormDialog.formData"
      :platforms="platforms"
      :core-services="coreServices"
      @submit="handleAccountFormSubmit"
    />

    <!-- 导入账号对话框 -->
    <import-accounts-dialog
      v-if="importDialog.visible"
      v-model:visible="importDialog.visible"
      :platforms="platforms"
      :core-services="coreServices"
      @import="handleImportAccounts"
    />

    <!-- 关联设备对话框 -->
    <link-device-dialog
      v-show="linkDeviceDialog.visible"
      v-model:visible="linkDeviceDialog.visible"
      :account="linkDeviceDialog.account"
      :devices="devices"
      :platform-apps="platformApps"
      :platforms="platforms"
      @link="handleLinkDevice"
    />

    <!-- 批量更新对话框 -->
    <batch-update-dialog
      v-if="batchUpdateDialog.visible"
      v-model:visible="batchUpdateDialog.visible"
      :selected-count="selectedAccountIds.length"
      @update="handleBatchUpdate"
    />

    <!-- 平台管理对话框 -->
    <platform-management-dialog
      v-if="platformManagementDialog.visible"
      v-model:visible="platformManagementDialog.visible"
      @refresh="handlePlatformsRefresh"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import {
  getAccounts,
  getPlatforms,
  getPlatformApps,
  createAccount,
  updateAccount,
  copyAccount,
  deleteAccount,
  batchDeleteAccounts,
  batchUpdateAccounts,
  exportAccounts,
  linkDeviceAccount,
  getAccountDevices,
  openAccountDevice
} from '@/api/social'
import { getDevices } from '@/api/rest/device'
import { getCoreServices } from '@/api/core'
import AccountFormDialog from './components/AccountFormDialog.vue'
import ImportAccountsDialog from './components/ImportAccountsDialog.vue'
import LinkDeviceDialog from './components/LinkDeviceDialog.vue'
import BatchUpdateDialog from './components/BatchUpdateDialog.vue'
import PlatformManagementDialog from './components/PlatformManagementDialog.vue'
import type { SocialAccount, SocialPlatform, PlatformApp } from '@/types/social'
import { calculateExpiryStatus, formatDate as formatExpiryDate } from '@/utils/accountExpiry'

// 状态
const loading = ref(false)
const accounts = ref<SocialAccount[]>([])
const platforms = ref<SocialPlatform[]>([])
const coreServices = ref<any[]>([])
const devices = ref<any[]>([])
const platformApps = ref<PlatformApp[]>([])
const selectedAccountIds = ref<string[]>([])
const deviceOpeningStatus = ref<Record<string, boolean>>({}) // 设备打开状态

// 筛选条件
const filters = reactive({
  platformId: '',
  coreServiceId: '',
  status: '',
  keyword: ''
})

// 监听筛选条件变化
watch(() => filters.platformId, (newVal) => {
  console.log('平台筛选条件变化:', newVal)
  // 如果是ObjectId格式，尝试在平台列表中查找对应的平台
  if (newVal && newVal.length === 24 && /^[0-9a-f]{24}$/i.test(newVal)) {
    const platform = platforms.value.find(p => (p as any)._id === newVal || p.id === newVal)
    if (platform) {
      console.log(`找到平台: ${platform.name}，ID: ${platform.id}`)
    } else {
      console.warn(`未找到ID为 ${newVal} 的平台`)
    }
  }
})

watch(() => filters.coreServiceId, (newVal) => {
  console.log('Core服务筛选条件变化:', newVal)
  // 如果有值，尝试在Core服务列表中查找对应的服务
  if (newVal) {
    const service = coreServices.value.find(s => s.id === newVal)
    if (service) {
      console.log(`找到Core服务: ${service.name}，ID: ${service.id}`)
    } else {
      console.warn(`未找到ID为 ${newVal} 的Core服务`)
    }
  }
})

watch(() => filters.status, (newVal) => {
  console.log('状态筛选条件变化:', newVal)
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
  showPagination: true // 添加控制分页显示的标志
})

// 对话框状态
const accountFormDialog = reactive({
  visible: false,
  title: '添加账号',
  formData: {} as Partial<SocialAccount>
})

const importDialog = reactive({
  visible: false
})

const linkDeviceDialog = reactive({
  visible: false,
  account: {} as SocialAccount
})

const batchUpdateDialog = reactive({
  visible: false
})

const platformManagementDialog = reactive({
  visible: false
})

// 获取账号列表
const fetchAccounts = async () => {
  loading.value = true
  try {
    // 创建一个新的参数对象，避免修改原始筛选条件
    const params: Record<string, any> = {
      skip: (pagination.page - 1) * pagination.pageSize,
      limit: pagination.pageSize
    }

    // 只添加有值的筛选条件，并确保格式正确
    if (filters.platformId) {
      // 如果是ObjectId格式，尝试在平台列表中查找对应的平台
      if (filters.platformId.length === 24 && /^[0-9a-f]{24}$/i.test(filters.platformId)) {
        const platform = platforms.value.find(p => (p as any)._id === filters.platformId || p.id === filters.platformId)
        if (platform && platform.id) {
          // 使用平台的id字段，而不是_id
          params.platform_id = platform.id
          console.log(`使用平台ID: ${platform.id}，名称: ${platform.name}`)
        } else {
          params.platform_id = filters.platformId
          console.log(`未找到平台，直接使用ID: ${filters.platformId}`)
        }
      } else {
        params.platform_id = filters.platformId
      }
    }

    if (filters.coreServiceId) {
      // 确保Core服务ID是正确的格式
      params.core_service_id = filters.coreServiceId
      console.log(`使用Core服务ID: ${filters.coreServiceId}`)
    }

    if (filters.status) {
      params.status = filters.status
      console.log(`使用状态筛选: ${filters.status}`)
    }

    if (filters.keyword) {
      params.keyword = filters.keyword
    }

    console.log('获取账号列表，参数:', params)
    const res = await getAccounts(params)
    console.log('获取账号列表响应:', res)

    // 处理新的响应格式
    // 获取实际的响应数据（处理Axios响应对象）
    const responseData = res.data || res;

    console.log('实际响应数据:', responseData);

    // 检查是否需要获取账号关联的设备信息
    const needFetchDeviceInfo = true; // 始终获取设备信息

    if (responseData && typeof responseData === 'object' && 'data' in responseData && Array.isArray(responseData.data)) {
      // 新的响应格式
      accounts.value = responseData.data.map(account => {
        const formattedAccount = { ...account }

        // 确保账号有id字段
        if (!formattedAccount.id && (formattedAccount as any)._id) {
          formattedAccount.id = (formattedAccount as any)._id
        }

        // 确保platform_id是字符串类型
        if (formattedAccount.platform_id && typeof formattedAccount.platform_id !== 'string') {
          formattedAccount.platform_id = String(formattedAccount.platform_id)
        }

        // 确保core_service_id是字符串类型
        if (formattedAccount.core_service_id && typeof formattedAccount.core_service_id !== 'string') {
          formattedAccount.core_service_id = String(formattedAccount.core_service_id)
        }

        return formattedAccount
      })

      // 使用后端返回的分页信息
      pagination.total = responseData.total || 0
      pagination.page = responseData.page || 1
      pagination.pageSize = responseData.page_size || 20
      pagination.showPagination = true // 始终显示分页控件

      console.log(`更新分页信息: 当前页 ${pagination.page}, 每页 ${pagination.pageSize}, 总记录数 ${pagination.total}`)
    } else {
      // 旧的响应格式，兼容处理
      // 如果responseData不是新格式，但可能是数组或其他格式
      let accountsData = responseData;

      // 如果responseData有data属性但不是数组中的对象格式，尝试使用它
      if (responseData && typeof responseData === 'object' && 'data' in responseData && !Array.isArray(responseData.data)) {
        accountsData = responseData.data;
      }

      console.log('旧格式处理，账号数据:', accountsData);

      // 确保每个账号都有正确的ID字段
      const formattedAccounts = Array.isArray(accountsData) ? accountsData.map(account => {
        const formattedAccount = { ...account }

        // 确保账号有id字段
        if (!formattedAccount.id && (formattedAccount as any)._id) {
          formattedAccount.id = (formattedAccount as any)._id
        }

        // 确保platform_id是字符串类型
        if (formattedAccount.platform_id && typeof formattedAccount.platform_id !== 'string') {
          formattedAccount.platform_id = String(formattedAccount.platform_id)
        }

        // 确保core_service_id是字符串类型
        if (formattedAccount.core_service_id && typeof formattedAccount.core_service_id !== 'string') {
          formattedAccount.core_service_id = String(formattedAccount.core_service_id)
        }

        return formattedAccount
      }) : []

      accounts.value = formattedAccounts

      // 更新总记录数（旧方法）
      const currentPageCount = accounts.value.length;
      pagination.showPagination = true;

      // 如果当前页不是满页，说明已经到了最后一页
      if (currentPageCount < pagination.pageSize) {
        pagination.total = (pagination.page - 1) * pagination.pageSize + currentPageCount;
      } else {
        pagination.total = Math.max(pagination.total, pagination.page * pagination.pageSize);
      }

      // 确保总数至少等于当前页的记录数
      if (pagination.total < currentPageCount) {
        pagination.total = currentPageCount;
      }

      // 如果没有记录，但我们知道有分页，设置一个最小值
      if (pagination.total === 0 && pagination.page > 1) {
        pagination.total = pagination.pageSize;
      }

      console.log(`更新分页信息(旧方法): 当前页 ${pagination.page}, 每页 ${pagination.pageSize}, 当前页记录数 ${currentPageCount}, 估计总数 ${pagination.total}`);
    }

    console.log('处理后的账号数据:', accounts.value)

    // 检查账号数据结构
    if (accounts.value.length > 0) {
      console.log('账号数据示例:', accounts.value[0])
      console.log('账号ID字段类型:', typeof accounts.value[0].id)
      console.log('平台ID字段类型:', typeof accounts.value[0].platform_id)
      console.log('Core服务ID字段类型:', typeof accounts.value[0].core_service_id)
    }

    // 获取账号关联的设备信息
    console.log('开始获取账号关联的设备信息');

    // 为每个账号获取关联的设备信息
    const fetchDevicePromises = accounts.value.map(async (account) => {
      try {
        const response = await getAccountDevices(account.id);
        console.log(`账号 ${account.id} 关联的设备:`, response);

        if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {
          // 账号关联了设备
          const deviceMapping = response.data[0];
          account.linked_device_id = deviceMapping.device_id;

          // 查找设备名称
          const device = devices.value.find(d => d.id === deviceMapping.device_id);
          if (device) {
            account.device_name = device.name;
          }

          console.log(`账号 ${account.id} 关联了设备 ${account.linked_device_id}`);
        } else {
          // 账号未关联设备
          account.linked_device_id = null;
          account.device_name = null;
          console.log(`账号 ${account.id} 未关联设备`);
        }
      } catch (error) {
        console.error(`获取账号 ${account.id} 关联的设备失败:`, error);
        account.linked_device_id = null;
        account.device_name = null;
      }
    });

    // 等待所有请求完成
    await Promise.all(fetchDevicePromises);
    console.log('所有账号关联的设备信息获取完成');

  } catch (error) {
    console.error('获取账号列表失败:', error)
    ElMessage.error('获取账号列表失败')

    // 提供模拟数据以便前端开发可以继续进行
    accounts.value = [
      {
        id: '1',
        username: 'youtube_user1',
        display_name: 'YouTube用户1',
        platform_id: 'youtube',
        core_service_id: 'default',
        status: 'active',
        created_at: new Date().toISOString()
      },
      {
        id: '2',
        username: 'facebook_user1',
        display_name: 'Facebook用户1',
        platform_id: 'facebook',
        core_service_id: 'default',
        status: 'inactive',
        created_at: new Date().toISOString()
      },
      {
        id: '3',
        username: 'twitter_user1',
        display_name: 'Twitter用户1',
        platform_id: 'twitter',
        core_service_id: 'default',
        status: 'suspended',
        created_at: new Date().toISOString()
      }
    ]

    // 确保分页显示
    pagination.showPagination = true;
    pagination.total = Math.max(accounts.value.length, 20); // 设置一个最小值，确保分页控件显示
  } finally {
    loading.value = false
  }
}

// 获取平台列表
const fetchPlatforms = async () => {
  try {
    console.log('获取平台列表')
    const res = await getPlatforms()
    console.log('获取平台列表响应:', res)

    // 正确处理响应数据
    const platformsData = res.data || res
    console.log('平台原始数据:', platformsData)

    // 确保每个平台都有id字段
    const formattedPlatforms = Array.isArray(platformsData) ? platformsData.map(platform => {
      console.log('处理平台数据:', platform)

      // 创建新对象，避免修改原始对象
      const formattedPlatform = { ...platform }

      // 如果平台没有id字段，但有_id字段，则使用_id作为id
      if (!formattedPlatform.id && (formattedPlatform as any)._id) {
        formattedPlatform.id = String((formattedPlatform as any)._id)
        console.log(`平台ID从_id转换: ${(formattedPlatform as any)._id} -> ${formattedPlatform.id}`)
      }

      // 确保id是字符串类型
      if (formattedPlatform.id && typeof formattedPlatform.id !== 'string') {
        formattedPlatform.id = String(formattedPlatform.id)
        console.log(`平台ID转换为字符串: ${formattedPlatform.id}`)
      }

      // 根据平台名称设置标准ID
      if (formattedPlatform.name) {
        const name = formattedPlatform.name.toLowerCase()
        if (name.includes('youtube')) {
          formattedPlatform.id = 'youtube'
          console.log(`平台ID根据名称设置为: youtube`)
        } else if (name.includes('facebook')) {
          formattedPlatform.id = 'facebook'
          console.log(`平台ID根据名称设置为: facebook`)
        } else if (name.includes('twitter')) {
          formattedPlatform.id = 'twitter'
          console.log(`平台ID根据名称设置为: twitter`)
        } else if (name.includes('instagram')) {
          formattedPlatform.id = 'instagram'
          console.log(`平台ID根据名称设置为: instagram`)
        } else if (name.includes('tiktok')) {
          formattedPlatform.id = 'tiktok'
          console.log(`平台ID根据名称设置为: tiktok`)
        }
      }

      return formattedPlatform
    }) : []

    platforms.value = formattedPlatforms

    console.log('处理后的平台数据:', platforms.value)

    // 检查平台数据结构
    if (platforms.value.length > 0) {
      console.log('平台数据示例:', platforms.value[0])
      console.log('平台ID字段类型:', typeof platforms.value[0].id)

      // 检查账号数据中的平台ID是否与平台列表中的ID匹配
      if (accounts.value.length > 0) {
        // 使用Set去重
        const accountPlatformIds = [...new Set(accounts.value.map(account => account.platform_id))]
        console.log('账号中的平台ID:', accountPlatformIds)

        const platformIds = platforms.value.map(platform => platform.id)
        console.log('平台列表中的ID:', platformIds)

        // 检查每个账号的平台ID是否在平台列表中
        const matchingIds = accountPlatformIds.filter(id => platformIds.includes(id))
        console.log('匹配的平台ID数量:', matchingIds.length)

        // 如果有不匹配的平台ID，记录下来
        const nonMatchingIds = accountPlatformIds.filter(id => !platformIds.includes(id))
        if (nonMatchingIds.length > 0) {
          console.warn('以下平台ID在平台列表中不存在:', nonMatchingIds)
        }
      }
    } else {
      console.warn('没有找到平台数据')
    }
  } catch (error) {
    console.error('获取平台列表失败:', error)
    // 提供模拟数据以便前端开发可以继续进行
    platforms.value = [
      { id: 'youtube', name: 'YouTube', icon: 'youtube.png' },
      { id: 'facebook', name: 'Facebook', icon: 'facebook.png' },
      { id: 'twitter', name: 'Twitter', icon: 'twitter.png' },
      { id: 'instagram', name: 'Instagram', icon: 'instagram.png' },
      { id: 'tiktok', name: 'TikTok', icon: 'tiktok.png' }
    ]
    console.log('使用模拟平台数据:', platforms.value)
  }
}

// 获取Core服务列表
const fetchCoreServices = async () => {
  try {
    console.log('获取Core服务列表')
    const res = await getCoreServices()
    console.log('获取Core服务列表响应:', res)

    // 正确处理响应数据
    const coreServicesData = res.data || res

    // 确保每个Core服务都有id字段
    const formattedCoreServices = Array.isArray(coreServicesData) ? coreServicesData.map(service => {
      // 如果服务没有id字段，但有_id字段，则使用_id作为id
      if (!service.id && service._id) {
        return {
          ...service,
          id: service._id
        }
      }
      return service
    }) : []

    coreServices.value = formattedCoreServices

    console.log('处理后的Core服务数据:', coreServices.value)

    // 检查Core服务数据结构
    if (coreServices.value.length > 0) {
      console.log('Core服务数据示例:', coreServices.value[0])
      console.log('Core服务ID字段类型:', typeof coreServices.value[0].id)
    }
  } catch (error) {
    console.error('获取Core服务列表失败:', error)
    // 提供模拟数据以便前端开发可以继续进行
    coreServices.value = [
      { id: 'core1', name: 'Core服务 1' },
      { id: 'core2', name: 'Core服务 2' },
      { id: 'core3', name: 'Core服务 3' }
    ]
  }
}

// 获取设备列表
const fetchDevices = async () => {
  try {
    console.log('开始获取设备列表...')

    // 使用REST API获取设备列表，确保包含关联信息
    const response = await getDevices({
      include_config: true,
      include_mappings: true  // 确保包含设备的关联信息
    })

    console.log('获取设备列表响应:', response)

    // 检查设备是否包含mappings属性
    if (Array.isArray(response)) {
      const hasMappings = response.some(device => device.mappings && Array.isArray(device.mappings))
      console.log(`设备是否包含mappings属性: ${hasMappings}`)

      // 输出所有设备的mappings信息
      response.forEach(device => {
        const mappings = device.mappings || []
        console.log(`设备 ${device.name || device.id} (ID: ${device.id}) 有 ${mappings.length} 个关联:`, mappings)
      })
    }

    // 处理返回的数据结构
    let deviceData: any[] = []

    // 安全地处理各种可能的响应格式
    if (response && typeof response === 'object') {
      if ('data' in response && response.data) {
        const resData = response.data;
        console.log('设备响应中的data字段:', resData)

        if (typeof resData === 'object' && 'data' in resData && Array.isArray(resData.data)) {
          // 处理 {data: {data: Device[]}} 格式
          deviceData = resData.data;
          console.log('从嵌套data字段中提取设备数据:', deviceData)
        } else if (Array.isArray(resData)) {
          // 处理 {data: Device[]} 格式
          deviceData = resData;
          console.log('从data数组中提取设备数据:', deviceData)
        }
      } else if (Array.isArray(response)) {
        // 处理 Device[] 格式
        deviceData = response;
        console.log('直接使用响应数组作为设备数据:', deviceData)
      }
    }

    // 确保设备数据是数组，并且每个设备都有id和name字段
    if (Array.isArray(deviceData)) {
      // 过滤掉没有id的设备
      deviceData = deviceData.filter(device => device && device.id)

      // 确保每个设备都有name字段
      deviceData = deviceData.map(device => ({
        ...device,
        name: device.name || `设备 ${device.id}`
      }))

      devices.value = deviceData
      console.log('处理后的设备数据:', devices.value)

      if (devices.value.length === 0) {
        console.warn('没有找到任何设备，使用模拟数据')
        // 如果没有设备数据，使用模拟数据
        devices.value = [
          { id: 'device1', name: '设备 1', status: 'online' },
          { id: 'device2', name: '设备 2', status: 'offline' },
          { id: 'device3', name: '雷电模拟器 1', status: 'online' },
          { id: 'device4', name: '雷电模拟器 2', status: 'online' },
          { id: 'device5', name: '夜神模拟器', status: 'offline' }
        ]
      }
    } else {
      console.error('设备数据不是数组:', deviceData)
      // 使用模拟数据
      devices.value = [
        { id: 'device1', name: '设备 1', status: 'online' },
        { id: 'device2', name: '设备 2', status: 'offline' },
        { id: 'device3', name: '雷电模拟器 1', status: 'online' },
        { id: 'device4', name: '雷电模拟器 2', status: 'online' },
        { id: 'device5', name: '夜神模拟器', status: 'offline' }
      ]
    }
  } catch (error) {
    console.error('获取设备列表失败:', error)
    // 提供更多模拟数据
    devices.value = [
      { id: 'device1', name: '设备 1', status: 'online' },
      { id: 'device2', name: '设备 2', status: 'offline' },
      { id: 'device3', name: '雷电模拟器 1', status: 'online' },
      { id: 'device4', name: '雷电模拟器 2', status: 'online' },
      { id: 'device5', name: '夜神模拟器', status: 'offline' }
    ]
  }

  // 确保设备列表不为空
  if (!devices.value || devices.value.length === 0) {
    console.warn('设备列表为空，使用默认模拟数据')
    devices.value = [
      { id: 'device1', name: '设备 1', status: 'online' },
      { id: 'device2', name: '设备 2', status: 'offline' },
      { id: 'device3', name: '雷电模拟器 1', status: 'online' },
      { id: 'device4', name: '雷电模拟器 2', status: 'online' },
      { id: 'device5', name: '夜神模拟器', status: 'offline' }
    ]
  }

  console.log('最终设备列表:', devices.value)
}

// 获取平台应用列表
const fetchPlatformApps = async () => {
  try {
    console.log('获取平台应用列表')

    // 创建一个临时数组存储所有平台的应用
    let allApps: PlatformApp[] = []

    // 为每个平台获取应用
    for (const platform of platforms.value) {
      try {
        console.log(`获取平台 ${platform.name} (${platform.id}) 的应用列表`)
        const res = await getPlatformApps(platform.id)
        console.log(`平台 ${platform.name} 应用列表响应:`, res)

        // 处理响应数据
        const appsData = res.data || res

        if (Array.isArray(appsData) && appsData.length > 0) {
          // 确保每个应用都有正确的平台ID
          const formattedApps = appsData.map(app => ({
            ...app,
            platform_id: platform.id // 确保平台ID正确
          }))

          allApps = [...allApps, ...formattedApps]
          console.log(`添加 ${formattedApps.length} 个应用到列表`)
        } else {
          console.log(`平台 ${platform.name} 没有应用`)
        }
      } catch (error) {
        console.error(`获取平台 ${platform.name} 应用列表失败:`, error)
      }
    }

    platformApps.value = allApps
    console.log('所有平台应用列表:', platformApps.value)

    // 如果没有获取到任何应用，添加一些模拟数据
    if (platformApps.value.length === 0) {
      console.warn('没有找到任何平台应用，使用模拟数据')

      // 为每个平台创建一个模拟应用
      const mockApps = platforms.value.map(platform => ({
        id: `app_${platform.id}`,
        platform_id: platform.id,
        name: `${platform.name} 应用`,
        type: 'android', // 使用有效的类型值
        status: 'active'
      })) as PlatformApp[]

      platformApps.value = mockApps
      console.log('模拟平台应用列表:', platformApps.value)
    }
  } catch (error) {
    console.error('获取平台应用列表失败:', error)

    // 提供模拟数据
    platformApps.value = [
      { id: 'app1', platform_id: 'youtube', name: 'YouTube 应用', type: 'android', status: 'active' },
      { id: 'app2', platform_id: 'facebook', name: 'Facebook 应用', type: 'ios', status: 'active' },
      { id: 'app3', platform_id: 'twitter', name: 'Twitter 应用', type: 'web', status: 'active' }
    ] as PlatformApp[]
  }
}

// 工具函数
const getPlatformName = (platformId: string) => {
  // 尝试通过id或_id字段匹配平台
  const platform = platforms.value.find(p =>
    p.id === platformId ||
    String(p._id) === platformId ||
    (p as any)._id === platformId
  )

  if (platform) {
    return platform.name
  }

  // 如果没有找到平台，使用硬编码的映射表作为后备
  const platformIdMap: Record<string, string> = {
    '681efeeecd836bd64b9c2a1e': 'YouTube',
    '681efeeecd836bd64b9c2a20': 'TikTok',
    '681efeeecd836bd64b9c2a22': '抖音',
    '6822ecaa62fd956eb6d2c071': 'Facebook',
    '6822ebfc05340d5a3d867138': 'AWS',
    'youtube': 'YouTube',
    'facebook': 'Facebook',
    'tiktok': 'TikTok',
    'instagram': 'Instagram',
    'twitter': 'Twitter'
  }

  return platformIdMap[platformId] || platformId
}

const getPlatformIcon = (platformId: string) => {
  // 尝试通过id或_id字段匹配平台
  const platform = platforms.value.find(p =>
    p.id === platformId ||
    String(p._id) === platformId ||
    (p as any)._id === platformId
  )
  if (!platform || !platform.icon) return ''

  const icon = platform.icon

  // 如果图标是完整URL（包含http或https）
  if (icon.startsWith('http://') || icon.startsWith('https://')) {
    return icon
  }
  // 如果图标路径已经以/开头（标准URL路径）
  else if (icon.startsWith('/')) {
    // 确保不会出现双重/icons/前缀
    if (icon.includes('/icons/') && icon.startsWith('/icons/')) {
      return icon
    } else if (icon.includes('/icons/')) {
      // 如果包含/icons/但不是以它开头，提取文件名
      const parts = icon.split('/icons/')
      return `/icons/${parts[parts.length - 1]}`
    } else {
      return icon
    }
  }
  // 如果图标路径包含icons/前缀但不是以/开头
  else if (icon.includes('icons/')) {
    // 提取文件名
    const parts = icon.split('icons/')
    return `/icons/${parts[parts.length - 1]}`
  }
  // 如果图标只是名称，拼接路径（兼容旧数据）
  else {
    return `/icons/${icon}`
  }
}

const getCoreServiceName = (coreServiceId: string) => {
  const service = coreServices.value.find(s => s.id === coreServiceId)
  return service ? service.name : coreServiceId
}

const getDeviceName = (deviceId: string) => {
  if (!deviceId) return '未知设备'
  const device = devices.value.find(d => d.id === deviceId)
  return device ? device.name : `设备 ${deviceId}`
}

const getStatusText = (status?: string) => {
  const statusMap: Record<string, string> = {
    active: '活跃',
    inactive: '非活跃',
    suspended: '已暂停'
  }
  return status ? statusMap[status] || status : '未知'
}

const getStatusType = (status?: string) => {
  const typeMap: Record<string, string> = {
    active: 'success',
    inactive: 'info',
    suspended: 'warning'
  }
  return status ? typeMap[status] || 'info' : 'info'
}

const formatDate = (dateStr?: string) => {
  if (!dateStr) return '未知'
  try {
    return format(new Date(dateStr), 'yyyy-MM-dd HH:mm:ss')
  } catch {
    return dateStr
  }
}

// 获取账号过期状态信息
const getExpiryInfo = (lastUpdatedDate?: string) => {
  return calculateExpiryStatus(lastUpdatedDate)
}

// 获取表格行的类名，用于设置整行背景色
const getRowClassName = ({ row }: { row: SocialAccount }) => {
  const expiryInfo = calculateExpiryStatus(row.last_updated_date)

  switch (expiryInfo.status) {
    case 'expired':
      return 'row-expired'
    case 'warning_1':
      return 'row-warning-1'
    case 'warning_2':
      return 'row-warning-2'
    case 'warning_3':
      return 'row-warning-3'
    default:
      return ''
  }
}

// 获取过期状态文本的类名
const getExpiryStatusTextClass = (lastUpdatedDate?: string) => {
  const expiryInfo = calculateExpiryStatus(lastUpdatedDate)

  switch (expiryInfo.status) {
    case 'expired':
      return 'expiry-text-expired'
    case 'warning_1':
      return 'expiry-text-warning-1'
    case 'warning_2':
      return 'expiry-text-warning-2'
    case 'warning_3':
      return 'expiry-text-warning-3'
    default:
      return 'expiry-text-normal'
  }
}

// 按更新日期排序
const sortByUpdateDate = (a: SocialAccount, b: SocialAccount) => {
  // 未设置日期的排在最后
  if (!a.last_updated_date && !b.last_updated_date) return 0
  if (!a.last_updated_date) return 1
  if (!b.last_updated_date) return -1

  // 按日期排序，最近的日期排在前面
  const dateA = new Date(a.last_updated_date)
  const dateB = new Date(b.last_updated_date)

  return dateB.getTime() - dateA.getTime()
}

// 按过期状态排序
const sortByExpiryStatus = (a: SocialAccount, b: SocialAccount) => {
  const statusA = calculateExpiryStatus(a.last_updated_date)
  const statusB = calculateExpiryStatus(b.last_updated_date)

  // 定义状态优先级（数字越小优先级越高）
  const statusPriority = {
    'expired': 1,      // 已过期 - 最高优先级
    'warning_1': 2,    // 1天内过期
    'warning_2': 3,    // 2天内过期
    'warning_3': 4,    // 3天内过期
    'normal': 5        // 正常状态 - 最低优先级
  }

  const priorityA = statusPriority[statusA.status as keyof typeof statusPriority] || 6
  const priorityB = statusPriority[statusB.status as keyof typeof statusPriority] || 6

  // 如果优先级相同，按剩余天数排序（天数少的排前面）
  if (priorityA === priorityB) {
    return statusA.daysRemaining - statusB.daysRemaining
  }

  return priorityA - priorityB
}

// 快速按过期状态排序
const quickSortByExpiry = () => {
  accounts.value.sort(sortByExpiryStatus)
  ElMessage.success('已按过期状态排序（紧急程度从高到低）')
}

// 快速按更新日期排序
const quickSortByUpdateDate = () => {
  accounts.value.sort(sortByUpdateDate)
  ElMessage.success('已按更新日期排序（最新日期在前）')
}

// 获取账号显示名称
const getAccountDisplayName = (account: any) => {
  // 优先级：display_name > username > tags[0] > ID
  if (account.display_name && account.display_name.trim()) {
    return account.display_name.trim()
  }
  if (account.username && account.username.trim()) {
    return account.username.trim()
  }
  if (account.tags && account.tags.length > 0 && account.tags[0].trim()) {
    return account.tags[0].trim()
  }
  return `账号${account.id || 'Unknown'}`
}

// 事件处理
const handleSearch = () => {
  console.log('执行搜索，原始筛选条件:', {
    platformId: filters.platformId,
    coreServiceId: filters.coreServiceId,
    status: filters.status,
    keyword: filters.keyword
  })

  // 清除空的筛选条件
  if (filters.platformId === '') {
    filters.platformId = ''
  }

  if (filters.coreServiceId === '') {
    filters.coreServiceId = ''
  }

  if (filters.status === '') {
    filters.status = ''
  }

  if (filters.keyword === '') {
    filters.keyword = ''
  }

  console.log('执行搜索，处理后的筛选条件:', {
    platformId: filters.platformId,
    coreServiceId: filters.coreServiceId,
    status: filters.status,
    keyword: filters.keyword
  })

  pagination.page = 1
  fetchAccounts()
}

// 实时搜索处理函数（防抖）
let searchTimeout: NodeJS.Timeout | null = null
const handleSearchInput = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }

  searchTimeout = setTimeout(() => {
    console.log('执行实时搜索，关键词:', filters.keyword)
    pagination.page = 1
    fetchAccounts()
  }, 500) // 500ms防抖
}

const resetFilters = () => {
  // 重置所有筛选条件
  Object.keys(filters).forEach(key => {
    filters[key as keyof typeof filters] = ''
  })

  console.log('重置筛选条件')

  // 直接获取所有账号，而不是调用handleSearch
  pagination.page = 1
  fetchAccounts()
}

const handleSelectionChange = (selection: SocialAccount[]) => {
  selectedAccountIds.value = selection.map(item => item.id)
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  fetchAccounts()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchAccounts()
}

const showAddAccountDialog = () => {
  accountFormDialog.title = '添加账号'
  accountFormDialog.formData = {
    status: 'active',
    core_service_id: coreServices.value.length > 0 ? coreServices.value[0].id : ''
  }
  accountFormDialog.visible = true
}

const handleEdit = (account: SocialAccount) => {
  accountFormDialog.title = '编辑账号'
  accountFormDialog.formData = { ...account }
  accountFormDialog.visible = true
}

const handleAccountFormSubmit = async (formData: Partial<SocialAccount>) => {
  try {
    if (formData.id) {
      // 更新账号
      await updateAccount(formData.id, formData)
      ElMessage.success('更新账号成功')
    } else {
      // 创建账号
      await createAccount(formData as Omit<SocialAccount, 'id'>)
      ElMessage.success('创建账号成功')
    }
    accountFormDialog.visible = false
    fetchAccounts()
  } catch (error) {
    console.error('保存账号失败:', error)
    ElMessage.error('保存账号失败')
  }
}

const handleCopy = async (id: string) => {
  try {
    await ElMessageBox.confirm('确定要复制该账号吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })

    await copyAccount(id)
    ElMessage.success('复制账号成功')
    fetchAccounts()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('复制账号失败:', error)
      ElMessage.error('复制账号失败')
    }
  }
}

const handleDelete = async (id: string) => {
  try {
    await ElMessageBox.confirm('确定要删除该账号吗？此操作不可恢复。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await deleteAccount(id)
    ElMessage.success('删除账号成功')
    fetchAccounts()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除账号失败:', error)
      ElMessage.error('删除账号失败')
    }
  }
}

const handleBatchDelete = async () => {
  if (selectedAccountIds.value.length === 0) return

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedAccountIds.value.length} 个账号吗？此操作不可恢复。`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await batchDeleteAccounts(selectedAccountIds.value)
    ElMessage.success('批量删除账号成功')
    selectedAccountIds.value = []
    fetchAccounts()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除账号失败:', error)
      ElMessage.error('批量删除账号失败')
    }
  }
}

const showImportDialog = () => {
  importDialog.visible = true
}

const handleImportAccounts = async (result: any) => {
  ElMessage.success(`成功导入 ${result.imported_count} 个账号`)
  importDialog.visible = false
  fetchAccounts()
}

const handleExport = async () => {
  try {
    // 检查是否有选中的账号
    const hasSelectedAccounts = selectedAccountIds.value.length > 0;

    // 准备导出参数
    const params = {
      platform_id: filters.platformId || undefined,
      core_service_id: filters.coreServiceId || undefined,
      format: 'csv' as 'csv', // 使用CSV格式，明确指定类型
      account_ids: hasSelectedAccounts ? selectedAccountIds.value : undefined
    }

    // 显示导出提示
    ElMessage.info(hasSelectedAccounts
      ? `正在导出 ${selectedAccountIds.value.length} 个选中的账号...`
      : '正在导出所有符合筛选条件的账号...');

    const response = await exportAccounts(params)

    // 从响应头中获取文件名
    let filename = 'accounts_export.csv'
    const contentDisposition = response.headers?.['content-disposition']
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/)
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1]
      }
    }

    // 检查响应类型
    let blob;
    if (response.data instanceof Blob) {
      // 如果已经是Blob类型，直接使用
      blob = response.data;
    } else {
      // 否则，创建一个新的Blob
      try {
        // 对于JSON响应，格式化为字符串
        const data = typeof response.data === 'object'
          ? JSON.stringify(response.data, null, 2)
          : response.data;
        blob = new Blob([data], { type: 'text/csv' });
      } catch (error) {
        console.error('创建Blob失败:', error);
        blob = new Blob(['导出失败'], { type: 'text/plain' });
      }
    }

    // 创建下载链接
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 释放URL对象
    setTimeout(() => URL.revokeObjectURL(url), 100)

    // 显示成功消息
    if (params.account_ids && params.account_ids.length > 0) {
      ElMessage.success(`成功导出 ${params.account_ids.length} 个选中的账号`)
    } else {
      ElMessage.success('账号导出成功')
    }
  } catch (error) {
    console.error('导出账号失败:', error)
    ElMessage.error('导出账号失败')
  }
}

const showLinkDeviceDialog = (account: SocialAccount) => {
  // 创建账号对象的副本，避免修改原始对象
  const accountCopy = { ...account }

  console.log('打开设备关联对话框，原始账号信息:', account)

  // 保存原始的_id字段
  if ((accountCopy as any)._id) {
    // 确保_id是字符串类型
    if (typeof (accountCopy as any)._id !== 'string') {
      (accountCopy as any)._id = String((accountCopy as any)._id)
    }
    console.log(`账号_id: ${(accountCopy as any)._id}`)
  } else if (accountCopy.id) {
    // 如果没有_id字段，但有id字段，则使用id作为_id
    (accountCopy as any)._id = accountCopy.id
    console.log(`使用id作为_id: ${(accountCopy as any)._id}`)
  }

  // 确保id字段存在
  if (!accountCopy.id && (accountCopy as any)._id) {
    accountCopy.id = (accountCopy as any)._id
    console.log(`账号ID从_id转换: ${(accountCopy as any)._id} -> ${accountCopy.id}`)
  }

  // 确保id是字符串类型
  if (accountCopy.id && typeof accountCopy.id !== 'string') {
    accountCopy.id = String(accountCopy.id)
    console.log(`账号ID转换为字符串: ${accountCopy.id}`)
  }

  // 添加平台名称字段，方便在对话框中显示
  if (accountCopy.platform_id) {
    // 确保platform_id是字符串类型
    if (typeof accountCopy.platform_id !== 'string') {
      accountCopy.platform_id = String(accountCopy.platform_id)
      console.log(`平台ID转换为字符串: ${accountCopy.platform_id}`)
    }

    // 查找平台信息
    const platform = platforms.value.find(p =>
      p.id === accountCopy.platform_id ||
      String(p._id) === accountCopy.platform_id
    )

    if (platform) {
      // 使用类型断言添加平台名称
      (accountCopy as any).platform_name = platform.name
      console.log(`找到平台: ${platform.name}，ID: ${platform.id}`)
    } else {
      console.warn(`未找到ID为 ${accountCopy.platform_id} 的平台`)
    }
  }

  console.log('处理后的账号信息:', accountCopy)

  // 先设置账号信息
  linkDeviceDialog.account = accountCopy

  // 设置对话框可见性
  console.log('设置对话框可见性')
  linkDeviceDialog.visible = true

  // 添加延迟，确保对话框完全打开
  setTimeout(() => {
    console.log('延迟执行，手动触发刷新')
    // 手动触发一次账号关联设备的API调用
    getAccountDevices(String((accountCopy as any)._id || accountCopy.id))
      .then(response => {
        console.log('手动触发API调用结果:', response)
      })
      .catch(error => {
        console.error('手动触发API调用失败:', error)
      })
  }, 800)
}

const handleLinkDevice = async (linkData: any) => {
  try {
    console.log('关联设备数据:', linkData);

    // 确保所有ID都是字符串类型
    const deviceId = String(linkData.device_id || '')
    const accountId = String(linkData.account_id || '')
    const platformId = String(linkData.platform_id || '')
    const appId = String(linkData.app_id || '')

    console.log('处理后的关联数据:', {
      device_id: deviceId,
      account_id: accountId,
      platform_id: platformId,
      app_id: appId
    })

    // 调用关联设备API
    await linkDeviceAccount({
      device_id: deviceId,
      account_id: accountId,
      platform_id: platformId,
      app_id: appId,
      settings: linkData.settings || {
        auto_login: true,
        keep_alive: true
      }
    })

    ElMessage.success('关联设备成功')
    linkDeviceDialog.visible = false
    fetchAccounts()
  } catch (error) {
    console.error('关联设备失败:', error)
    ElMessage.error('关联设备失败: ' + (error instanceof Error ? error.message : String(error)))
  }
}

const handleOpenDevice = async (account: SocialAccount) => {
  try {
    console.log('打开设备，账号信息:', account)

    // 检查账号是否有关联设备
    if (!account.device_name && !account.linked_device_id) {
      ElMessage.warning('该账号未关联设备，请先关联设备')
      return
    }

    // 设置加载状态
    deviceOpeningStatus.value[account.id] = true

    // 调用打开设备API
    const result = await openAccountDevice(account.id)

    if (result.success) {
      ElMessage.success('设备启动成功，V2rayN已连接')
    } else {
      ElMessage.error(result.message || '设备启动失败')
    }
  } catch (error) {
    console.error('打开设备失败:', error)
    ElMessage.error('打开设备失败: ' + (error instanceof Error ? error.message : String(error)))
  } finally {
    // 清除加载状态
    deviceOpeningStatus.value[account.id] = false
  }
}

const showBatchUpdateDialog = () => {
  if (selectedAccountIds.value.length === 0) return
  batchUpdateDialog.visible = true
}

// 显示平台管理对话框
const showPlatformManagementDialog = () => {
  platformManagementDialog.visible = true
}

// 平台刷新后的处理
const handlePlatformsRefresh = () => {
  // 重新加载平台列表
  fetchPlatforms()
}

const handleBatchUpdate = async (updateData: Partial<SocialAccount>) => {
  try {
    const result = await batchUpdateAccounts(selectedAccountIds.value, updateData)
    // 处理响应结果，尝试获取更新的账号数量
    const updatedCount = (result as any).updated_count ||
                         (result as any).data?.updated_count ||
                         selectedAccountIds.value.length
    ElMessage.success(`成功更新 ${updatedCount} 个账号`)
    batchUpdateDialog.visible = false
    fetchAccounts()
  } catch (error) {
    console.error('批量更新账号失败:', error)
    ElMessage.error('批量更新账号失败')
  }
}

// 生命周期
onMounted(async () => {
  // 先获取平台列表
  await fetchPlatforms()

  // 然后获取平台应用列表（依赖平台列表）
  await fetchPlatformApps()

  // 获取其他数据
  fetchCoreServices()
  fetchDevices()
  fetchAccounts()
})
</script>

<style scoped>
.account-management {
  padding: 20px;
  height: calc(100vh - 40px); /* 减去padding的高度 */
  display: flex;
  flex-direction: column;
  overflow: auto; /* 改为auto，允许滚动 */
}

.page-title {
  margin-bottom: 20px;
  color: #4361ee;
}

.filter-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

/* 增加下拉菜单宽度 */
:deep(.el-select) {
  width: 200px;
}

:deep(.el-select-dropdown) {
  min-width: 200px !important;
}

:deep(.wider-dropdown) {
  min-width: 250px !important;
}

.platform-option, .service-option, .status-option {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.platform-select-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.service-info {
  font-size: 12px;
  color: #999;
  margin-left: auto;
}

.action-section {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.action-pagination {
  margin-left: auto;
}

.platform-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.platform-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.device-link-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.device-link-tag {
  white-space: nowrap;
}

.device-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.account-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}

.no-tags {
  color: #909399;
  font-size: 12px;
}

.table-container {
  flex: 1;
  min-height: 300px; /* 设置最小高度 */
  max-height: calc(100vh - 350px); /* 设置最大高度，确保留出空间给分页 */
  overflow: auto; /* 允许内容滚动 */
  margin-bottom: 0; /* 移除底部边距，由分页容器提供间距 */
  border: 1px solid #ebeef5; /* 添加边框 */
  border-radius: 4px; /* 添加圆角 */
  background-color: #fff; /* 确保背景为白色 */
}

.table-pagination {
  padding: 15px 0px 15px 0px; /* 增加左侧padding，使分页器整体向左移动 */
  display: flex;
  justify-content: flex-end; /* 改为左对齐 */
  background-color: #fff;
  border-top: 1px solid #ebeef5;
  width: 100%;
}

/* 确保分页组件正确显示 */
:deep(.el-pagination) {
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end; /* 改为右对齐 */
  font-size: 13px; /* 稍微减小字体大小 */
}

/* 过期状态样式 */
.no-expiry {
  color: #909399;
  font-size: 12px;
}

/* 过期状态标签样式 */
:deep(.el-tag) {
  font-weight: 500;
  border-radius: 4px;
}

/* 表格行背景色 - 过期状态提醒 */
:deep(.el-table .row-expired) {
  background-color: #fef0f0 !important;
}

:deep(.el-table .row-expired:hover) {
  background-color: #fde2e2 !important;
}

:deep(.el-table .row-warning-1) {
  background-color: #fdf6ec !important;
}

:deep(.el-table .row-warning-1:hover) {
  background-color: #faecd8 !important;
}

:deep(.el-table .row-warning-2) {
  background-color: #fef5f5 !important;
}

:deep(.el-table .row-warning-2:hover) {
  background-color: #fce8e8 !important;
}

:deep(.el-table .row-warning-3) {
  background-color: #fffbf0 !important;
}

:deep(.el-table .row-warning-3:hover) {
  background-color: #fef7e6 !important;
}

/* 过期状态文本样式 */
.expiry-text-expired {
  color: #f56c6c;
  font-weight: 600;
}

.expiry-text-warning-1 {
  color: #e6a23c;
  font-weight: 600;
}

.expiry-text-warning-2 {
  color: #f78989;
  font-weight: 600;
}

.expiry-text-warning-3 {
  color: #b88230;
  font-weight: 500;
}

.expiry-text-normal {
  color: #67c23a;
  font-weight: 400;
}

/* 排序按钮样式 */
.sort-buttons {
  margin-left: 10px;
}

/* 设备操作样式 */
.device-link-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.device-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.device-name {
  font-size: 12px;
  color: #666;
  margin-left: 8px;
}

.device-link-tag {
  margin-right: 8px;
}

.sort-buttons .el-button {
  font-size: 12px;
  padding: 5px 10px;
}

/* 确保分页组件中的元素正确显示 */
:deep(.el-pagination__total),
:deep(.el-pagination__sizes),
:deep(.el-pagination__jump) {
  display: inline-block;
  margin: 0 10px;
}

/* 确保页码按钮水平排列 */
:deep(.el-pager) {
  display: flex;
  flex-direction: row;
  margin: 0 10px;
}

/* 确保每个页码按钮正确显示 */
:deep(.el-pager li) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 0 5px;
}
</style>