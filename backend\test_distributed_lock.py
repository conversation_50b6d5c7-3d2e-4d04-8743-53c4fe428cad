#!/usr/bin/env python3
"""
测试分布式锁功能的脚本
用于验证多个Backend服务实例之间的锁机制是否正常工作
"""

import asyncio
import redis.asyncio as redis
import logging
import time
import socket
import os
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DistributedLockTester:
    """分布式锁测试器"""
    
    def __init__(self, redis_url: str = "redis://***************:6379/1"):
        self.redis_client = redis.from_url(redis_url)
        self._current_locks = {}
    
    async def _try_acquire_distributed_lock(self, lock_key: str, expire_time: int = 60) -> bool:
        """尝试获取分布式锁"""
        try:
            # 生成唯一的锁值，包含服务实例信息
            hostname = socket.gethostname()
            pid = os.getpid()
            lock_value = f"backend_{hostname}_{pid}_{int(time.time())}"
            
            # 使用 SET NX EX 原子操作获取锁
            result = await self.redis_client.set(lock_key, lock_value, nx=True, ex=expire_time)
            
            if result:
                logger.info(f"✅ 成功获取分布式锁: {lock_key} (值: {lock_value})")
                # 将锁值存储起来，用于释放时验证
                self._current_locks[lock_key] = lock_value
                return True
            else:
                # 检查锁的持有者信息
                current_lock_value = await self.redis_client.get(lock_key)
                logger.info(f"❌ 锁已被占用: {lock_key} (持有者: {current_lock_value})")
                return False
                
        except Exception as e:
            logger.error(f"尝试获取分布式锁失败: {str(e)}")
            return False

    async def _release_distributed_lock(self, lock_key: str) -> bool:
        """释放分布式锁"""
        try:
            # 获取当前锁值
            expected_lock_value = self._current_locks.get(lock_key)
            
            if not expected_lock_value:
                logger.warning(f"尝试释放未持有的锁: {lock_key}")
                return False
            
            # 使用 Lua 脚本确保原子性：只有锁值匹配才删除
            lua_script = """
            if redis.call("get", KEYS[1]) == ARGV[1] then
                return redis.call("del", KEYS[1])
            else
                return 0
            end
            """
            
            result = await self.redis_client.eval(lua_script, 1, lock_key, expected_lock_value)
            
            if result == 1:
                logger.info(f"✅ 成功释放分布式锁: {lock_key}")
                # 从本地记录中移除
                self._current_locks.pop(lock_key, None)
                return True
            else:
                logger.warning(f"❌ 锁已被其他实例持有或已过期: {lock_key}")
                return False
                
        except Exception as e:
            logger.error(f"释放分布式锁失败: {str(e)}")
            return False

    async def simulate_task_processing(self, task_id: str, process_time: int = 5):
        """模拟任务处理过程"""
        lock_key = f"subtask_start_lock:{task_id}"
        
        logger.info(f"🎯 开始处理任务: {task_id}")
        
        # 尝试获取锁
        if await self._try_acquire_distributed_lock(lock_key):
            try:
                logger.info(f"🔒 获得锁，开始处理任务: {task_id}")
                
                # 模拟任务处理
                for i in range(process_time):
                    await asyncio.sleep(1)
                    logger.info(f"📊 任务 {task_id} 处理进度: {i+1}/{process_time}")
                
                logger.info(f"✅ 任务 {task_id} 处理完成")
                
            finally:
                # 释放锁
                await self._release_distributed_lock(lock_key)
        else:
            logger.info(f"❌ 无法获取锁，跳过任务: {task_id}")

    async def test_concurrent_access(self, task_id: str, num_workers: int = 3):
        """测试并发访问"""
        logger.info(f"🚀 开始并发测试: {num_workers} 个工作进程同时处理任务 {task_id}")
        
        # 创建多个并发任务
        tasks = []
        for i in range(num_workers):
            task = asyncio.create_task(
                self.simulate_task_processing(f"{task_id}_worker_{i}", 3)
            )
            tasks.append(task)
        
        # 等待所有任务完成
        await asyncio.gather(*tasks)
        logger.info("🎉 并发测试完成")

    async def cleanup(self):
        """清理资源"""
        await self.redis_client.close()

async def main():
    """主函数"""
    tester = DistributedLockTester()
    
    try:
        # 测试单个锁
        logger.info("=" * 50)
        logger.info("测试1: 单个锁获取和释放")
        await tester.simulate_task_processing("test_task_1", 3)
        
        # 测试并发访问
        logger.info("=" * 50)
        logger.info("测试2: 并发访问测试")
        await tester.test_concurrent_access("test_task_2", 5)
        
        logger.info("=" * 50)
        logger.info("所有测试完成!")
        
    finally:
        await tester.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
