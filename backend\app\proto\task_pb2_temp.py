# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: task.proto
"""Generated protocol buffer code."""

# 🔧 简化的task_pb2模块，包含必要的类

# 手动定义消息类
class TaskRequest:
    def __init__(self, task_id="", platform_id="", account_id="", device_id="", content_path="", workflow_id="", params=None, task_type=""):
        self.task_id = task_id
        self.platform_id = platform_id
        self.account_id = account_id
        self.device_id = device_id
        self.content_path = content_path
        self.workflow_id = workflow_id
        self.params = params or {}
        self.task_type = task_type

class TaskIdRequest:
    def __init__(self, task_id=""):
        self.task_id = task_id

class TaskResponse:
    def __init__(self, success=False, task_id="", error=""):
        self.success = success
        self.task_id = task_id
        self.error = error

class DeviceUsage:
    def __init__(self, cpu=0, memory=0, network=""):
        self.cpu = cpu
        self.memory = memory
        self.network = network

class TaskLog:
    def __init__(self, message="", level="", timestamp=""):
        self.message = message
        self.level = level
        self.timestamp = timestamp

class TaskStatusResponse:
    def __init__(self, task_id="", status="", progress=0, start_time="", estimated_end_time="", device_usage=None, logs=None):
        self.task_id = task_id
        self.status = status
        self.progress = progress
        self.start_time = start_time
        self.estimated_end_time = estimated_end_time
        self.device_usage = device_usage or DeviceUsage()
        self.logs = logs or []

class TaskLogsResponse:
    def __init__(self, task_id="", logs=None):
        self.task_id = task_id
        self.logs = logs or []

class WorkflowConfigRequest:
    def __init__(self, platform_id="", content_type=""):
        self.platform_id = platform_id
        self.content_type = content_type

class WorkflowStep:
    def __init__(self, id="", name="", description="", action="", required=True, timeout=30, retry_count=3, wait_after=0, condition="", element="", parameters="", notes=""):
        self.id = id
        self.name = name
        self.description = description
        self.action = action
        self.required = required
        self.timeout = timeout
        self.retry_count = retry_count
        self.wait_after = wait_after
        self.condition = condition
        self.element = element
        self.parameters = parameters
        self.notes = notes

class WorkflowConfigResponse:
    def __init__(self, success=False, error="", workflow_id="", workflow_name="", workflow_description="", workflow_version="", steps=None, config=""):
        self.success = success
        self.error = error
        self.workflow_id = workflow_id
        self.workflow_name = workflow_name
        self.workflow_description = workflow_description
        self.workflow_version = workflow_version
        self.steps = steps or []
        self.config = config
