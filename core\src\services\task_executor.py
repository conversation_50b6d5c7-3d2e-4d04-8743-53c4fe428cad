"""
任务执行器服务
负责执行发布任务，与雷电模拟器交互
"""

import os
import logging
import asyncio
import datetime
import json
import time
import random
from typing import Dict, List, Any, Optional
from enum import Enum

# 尝试导入redis.asyncio，如果失败则使用标准redis
try:
    import redis.asyncio as redis
    logger = logging.getLogger(__name__)
    logger.info("成功导入redis.asyncio模块")
except ImportError:
    import redis
    logger = logging.getLogger(__name__)
    logger.warning("redis.asyncio模块导入失败，使用标准redis模块")

    # 创建一个兼容层，使标准redis看起来像异步redis
    class AsyncRedisWrapper:
        def __init__(self, redis_client):
            self.redis_client = redis_client

        async def ping(self):
            return self.redis_client.ping()

        async def publish(self, channel, message):
            return self.redis_client.publish(channel, message)

        async def set(self, key, value):
            return self.redis_client.set(key, value)

        async def get(self, key):
            return self.redis_client.get(key)

        async def expire(self, key, time):
            return self.redis_client.expire(key, time)

    # 修改redis.from_url函数以返回包装的客户端
    original_from_url = redis.from_url
    def async_from_url(*args, **kwargs):
        client = original_from_url(*args, **kwargs)
        return AsyncRedisWrapper(client)
    redis.from_url = async_from_url

from src.devices.base import DeviceStatus
from .common.resource_manager import resource_manager, create_managed_task
from .common.workflow_engine import WorkflowEngine

# 避免循环导入
# from src.main_service import CoreMainService

# 导入YouTube任务执行器
from .youtube.youtube_task_executor import YouTubeTaskExecutor

logger = logging.getLogger(__name__)

class TaskExecutor:
    """任务执行器类"""

    def __init__(self, main_service: 'CoreMainService'):
        """初始化任务执行器

        Args:
            main_service: Core主服务实例
        """
        self.main_service = main_service
        self.tasks = {}  # 任务缓存
        self.running_tasks = {}  # 正在运行的任务
        self.task_logs = {}  # 任务日志
        self.redis_client = None
        self.is_initialized = False

        # 初始化YouTube任务执行器
        self.youtube_task_executor = YouTubeTaskExecutor(main_service)

        # 🔧 新增：平台名称缓存，避免重复查询
        self.platform_cache = {}  # platform_id -> platform_name

        # 任务执行状态
        self.task_status = {
            "pending": "等待执行",
            "running": "正在执行",
            "paused": "已暂停",
            "completed": "已完成",
            "failed": "执行失败",
            "canceled": "已取消"
        }

        logger.info("任务执行器初始化")

    async def initialize(self) -> bool:
        """初始化任务执行器"""
        try:
            # 尝试连接Redis
            redis_connected = await self._reconnect_redis()

            if not redis_connected:
                logger.warning("Redis连接失败，将使用内存存储任务状态")
                self.redis_client = None

            self.is_initialized = True
            logger.info("任务执行器初始化完成")
            return True

        except Exception as e:
            logger.error(f"初始化任务执行器异常: {str(e)}", exc_info=True)
            return False

    async def get_platform_name(self, platform_id: str) -> str:
        """获取平台名称

        Args:
            platform_id: 平台ID（可能是ObjectId或平台名称）

        Returns:
            str: 平台名称
        """
        try:
            # 检查缓存
            if platform_id in self.platform_cache:
                return self.platform_cache[platform_id]

            platform_name = "unknown"

            # 如果是24位字符串，可能是ObjectId，需要查询数据库
            if len(str(platform_id)) == 24:
                # TODO: 这里应该查询数据库获取平台信息
                # 暂时使用硬编码映射（应该改为查询数据库）
                platform_id_str = str(platform_id).lower()
                if platform_id_str == "681efeeecd836bd64b9c2a1e":
                    platform_name = "youtube"
                elif platform_id_str == "681efeeecd836bd64b9c2a22":
                    platform_name = "douyin"
                else:
                    logger.warning(f"未知的平台ObjectId: {platform_id}")
                    platform_name = "unknown"
            else:
                # 直接使用作为平台名称
                platform_name = str(platform_id).lower()

            # 缓存结果
            self.platform_cache[platform_id] = platform_name

            logger.info(f"平台ID {platform_id} -> 平台名称: {platform_name}")
            return platform_name

        except Exception as e:
            logger.error(f"获取平台名称失败: {str(e)}")
            return "unknown"

    async def create_task(self, task_data: Dict[str, Any]) -> bool:
        """创建任务

        Args:
            task_data: 任务数据

        Returns:
            bool: 是否成功
        """
        try:
            task_id = task_data.get("task_id")
            if not task_id:
                logger.error("任务数据缺少task_id字段")
                return False

            logger.info(f"📝 开始创建任务: {task_id}")
            logger.info(f"📋 任务数据: {task_data}")

            # 保存任务数据
            self.tasks[task_id] = task_data
            logger.info(f"💾 任务{task_id}已保存到内存，当前任务总数: {len(self.tasks)}")
            logger.info(f"🔍 内存中的任务列表: {list(self.tasks.keys())}")

            # 初始化任务日志
            self.task_logs[task_id] = []
            self.add_task_log(task_id, "任务已创建", "info")

            # 发布任务状态到Redis
            await self.publish_task_status(task_id)

            logger.info(f"✅ 任务{task_id}创建成功")
            return True

        except Exception as e:
            logger.error(f"❌ 创建任务异常: {str(e)}", exc_info=True)
            return False

    async def start_task(self, task_id: str) -> bool:
        """开始执行任务

        Args:
            task_id: 任务ID

        Returns:
            bool: 是否成功
        """
        try:
            logger.info(f"🚀 收到启动任务请求: {task_id}")
            logger.info(f"🔍 当前内存中的任务列表: {list(self.tasks.keys())}")
            logger.info(f"📊 内存中任务总数: {len(self.tasks)}")

            # 检查任务是否存在
            if task_id not in self.tasks:
                logger.error(f"❌ 任务{task_id}不存在")
                logger.error(f"🔍 可用任务: {list(self.tasks.keys())}")
                return False

            logger.info(f"✅ 找到任务{task_id}，准备启动")

            # 获取任务数据
            task = self.tasks[task_id]

            # 检查任务状态
            if task.get("status") == "running":
                logger.warning(f"任务{task_id}已经在运行")
                return True

            # 🔧 通用逻辑：检查任务是否需要设备
            task_type = task.get("task_type", "")
            device_id = task.get("device_id")

            logger.info(f"🔍 启动任务前检测: task_type='{task_type}', device_id='{device_id}'")

            # 检查是否为需要设备的任务类型
            device_required_tasks = ["single", "subtask", "benchmark_download", "benchmark_download_batch"]

            if task_type in device_required_tasks:
                # 需要设备的任务必须有device_id
                if not device_id:
                    error_msg = f"任务类型 '{task_type}' 需要设备支持，但未分配设备ID"
                    logger.error(error_msg)
                    self.add_task_log(task_id, error_msg, "error")
                    return False

                # 检查设备是否可用
                device_controller = self.main_service.ldplayer_manager.devices.get(device_id)
                if not device_controller:
                    logger.error(f"设备{device_id}不存在")
                    self.add_task_log(task_id, f"设备{device_id}不存在", "error")
                    return False

                device_status = await device_controller.get_status()
                if device_status != DeviceStatus.RUNNING:
                    # 尝试启动设备
                    logger.info(f"设备{device_id}未运行，尝试启动")
                    self.add_task_log(task_id, f"设备{device_id}未运行，尝试启动", "info")

                    start_success = await device_controller.start()
                    if not start_success:
                        logger.error(f"启动设备{device_id}失败")
                        self.add_task_log(task_id, f"启动设备{device_id}失败", "error")
                        return False

                    # 等待设备启动完成
                    for _ in range(30):  # 最多等待30秒
                        await asyncio.sleep(1)
                        status = await device_controller.get_status()
                        if status == DeviceStatus.RUNNING:
                            break
                    else:
                        logger.error(f"设备{device_id}启动超时")
                        self.add_task_log(task_id, f"设备{device_id}启动超时", "error")
                        return False
            else:
                logger.info(f"✅ 任务类型 '{task_type}' 无需设备支持，直接执行")

            # 更新任务状态
            task["status"] = "running"
            task["start_time"] = datetime.datetime.now().isoformat()

            # 计算预计完成时间（30分钟后）
            end_time = datetime.datetime.now() + datetime.timedelta(minutes=30)
            task["estimated_end_time"] = end_time.isoformat()

            # 初始化进度
            task["progress"] = 0

            # 添加日志
            self.add_task_log(task_id, "任务开始执行", "success")

            # 发布任务状态到Redis
            await self.publish_task_status(task_id)

            # 再次发布任务状态到Redis，确保状态更新被接收
            await asyncio.sleep(0.5)
            await self.publish_task_status(task_id)

            # 使用资源管理器创建受管理的任务协程
            task_coroutine = create_managed_task(self.execute_task(task_id))

            # 保存到running_tasks用于取消操作
            self.running_tasks[task_id] = task_coroutine

            # 添加完成回调来清理running_tasks引用
            def cleanup_running_task_reference(future):
                try:
                    if task_id in self.running_tasks:
                        del self.running_tasks[task_id]
                    logger.info(f"任务 {task_id} 从running_tasks中清理")
                except Exception as e:
                    logger.error(f"清理running_tasks中任务 {task_id} 失败: {str(e)}")

            task_coroutine.add_done_callback(cleanup_running_task_reference)

            # 记录详细日志
            logger.info(f"任务{task_id}状态已发布到Redis，channel: task:{task_id}:status")

            logger.info(f"任务{task_id}开始执行")
            return True

        except Exception as e:
            logger.error(f"开始执行任务异常: {str(e)}", exc_info=True)
            self.add_task_log(task_id, f"开始执行任务异常: {str(e)}", "error")
            return False

    async def execute_task(self, task_id: str) -> None:
        """执行任务

        Args:
            task_id: 任务ID
        """
        try:
            # 记录正在运行的任务
            self.running_tasks[task_id] = True

            # 获取任务数据
            task = self.tasks[task_id]

            # 检查任务类型
            task_type = task.get("task_type", "single")

            # 如果是主任务，需要执行子任务
            if task_type == "main":
                await self.execute_main_task(task_id, task)
                return

            # 如果是子任务，正常执行
            if task_type == "subtask":
                self.add_task_log(task_id, f"开始执行子任务 {task.get('subtask_index', 1)}", "info")

            # 获取设备ID
            device_id = task.get("device_id")

            # 获取内容路径
            content_path = task.get("content_path")

            # 获取平台ID
            platform_id = task.get("platform_id")

            # 获取账号ID
            account_id = task.get("account_id")

            # 添加日志
            if device_id:
                self.add_task_log(task_id, f"准备在设备{device_id}上执行任务", "info")
            else:
                self.add_task_log(task_id, f"准备执行无需设备的任务", "info")
            self.add_task_log(task_id, f"内容路径: {content_path}", "info")
            self.add_task_log(task_id, f"平台ID: {platform_id}", "info")
            self.add_task_log(task_id, f"账号ID: {account_id}", "info")

            # 根据平台ID选择不同的任务执行器
            # 记录原始平台ID
            logger.info(f"原始平台ID: {platform_id}, 类型: {type(platform_id)}")

            # 🔧 重要：基于任务类型和平台的清晰检测逻辑
            task_type = task.get("task_type", "")
            is_download_task = task_type in ["benchmark_download", "benchmark_download_batch"]
            is_collect_task = task_type == "collect"

            logger.info(f"📋 任务类型检测: task_type='{task_type}', is_download_task={is_download_task}, is_collect_task={is_collect_task}")
            logger.info(f"📁 任务内容路径: {content_path} (仅用于存储)")

            # 🔧 清晰的任务类型判断逻辑
            is_youtube = False  # 🔧 重要：初始化is_youtube变量

            if is_download_task:
                # 下载任务：不管平台ID是什么，都使用下载执行器
                logger.info(f"✅ 确认为下载任务，将使用下载执行器")

            elif is_collect_task:
                # 采集任务：使用采集执行器
                logger.info(f"✅ 确认为采集任务，将使用采集执行器")

            else:
                # 非下载、非采集任务：根据平台判断是否为YouTube上传任务
                platform_name = await self.get_platform_name(platform_id) if platform_id else "unknown"

                logger.info(f"🔍 非下载、非采集任务，检测平台: {platform_name}")

                # 根据平台名称判断
                logger.info(f"🔧 平台名称判断: platform_name='{platform_name}', type={type(platform_name)}")
                logger.info(f"🔧 YouTube检测: 'youtube' in platform_name = {'youtube' in platform_name if platform_name else False}")
                logger.info(f"🔧 YouTube检测: platform_name == 'youtube' = {platform_name == 'youtube' if platform_name else False}")

                if platform_name and ("youtube" in platform_name.lower() if isinstance(platform_name, str) else False):
                    is_youtube = True
                    logger.info(f"✅ 检测到YouTube上传任务，平台: {platform_name}")
                else:
                    logger.info(f"ℹ️ 检测到其他平台任务: {platform_name}")

                # 补充检测：检查任务数据中是否有YouTube特有的字段
                if not is_youtube and "metadata" in task and isinstance(task["metadata"], dict):
                    metadata = task["metadata"]
                    if "titleTemplate" in metadata or "privacyStatus" in metadata:
                        is_youtube = True
                        logger.info("✅ 通过元数据字段检测到YouTube任务")

                # 补充检测：检查任务是否有folder_path字段
                if not is_youtube and "folder_path" in task and not task.get("content_path"):
                    is_youtube = True
                    task["content_path"] = task.get("folder_path")
                    logger.info(f"✅ 通过folder_path字段检测到YouTube任务")

            # 🔧 执行对应的任务处理逻辑
            logger.info(f"🔧 最终任务类型判断结果: is_download_task={is_download_task}, is_collect_task={is_collect_task}, is_youtube={is_youtube}")

            # 🔧 重要：在开始执行前检查是否已被取消
            if task.get("cancel_requested"):
                logger.info(f"任务{task_id}在开始执行前已被取消")
                task["status"] = "canceled"
                task["end_time"] = datetime.datetime.now().isoformat()
                self.add_task_log(task_id, "任务在开始执行前已被取消", "warning")
                await self.publish_task_status(task_id)
                return

            if is_download_task:
                # 🔧 重要：下载任务优先处理，不管路径是否包含YouTube
                self.add_task_log(task_id, f"✅ 检测到下载任务，使用下载执行器 (任务类型: {task_type})", "info")
                logger.info(f"✅ 任务{task_id}是下载任务，使用下载任务执行器，任务类型: {task_type}")
                logger.info(f"下载任务路径: {content_path}")

                # 调用主服务的下载任务执行方法
                if task_type == "benchmark_download_batch":
                    # 批量下载任务
                    tasks = task.get("tasks", [task])  # 如果没有子任务列表，使用当前任务
                    success = await self.main_service.execute_batch_download_task(task_id, tasks)
                else:
                    # 单个下载任务
                    success = await self.main_service.execute_download_task(task_id, task)

                # 🔧 重要：下载任务处理完成后直接返回，不再执行后续的YouTube检测
                if success:
                    task["status"] = "completed"
                    task["progress"] = 100
                    task["end_time"] = datetime.datetime.now().isoformat()
                    self.add_task_log(task_id, "下载任务执行完成", "success")
                else:
                    task["status"] = "failed"
                    task["end_time"] = datetime.datetime.now().isoformat()
                    self.add_task_log(task_id, "下载任务执行失败", "error")

                # 发布最终状态到Redis
                await self.publish_task_status(task_id)
                return

            elif is_collect_task:
                # 🔧 重要：采集任务处理
                self.add_task_log(task_id, f"✅ 检测到采集任务，使用采集执行器 (任务类型: {task_type})", "info")
                logger.info(f"✅ 任务{task_id}是采集任务，使用采集任务执行器，任务类型: {task_type}")

                # 调用主服务的采集任务执行方法
                success = await self.main_service.execute_collect_task(task_id, task)

                # 🔧 重要：采集任务处理完成后直接返回
                if success:
                    task["status"] = "completed"
                    task["progress"] = 100
                    task["end_time"] = datetime.datetime.now().isoformat()
                    self.add_task_log(task_id, "采集任务执行完成", "success")
                else:
                    task["status"] = "failed"
                    task["end_time"] = datetime.datetime.now().isoformat()
                    self.add_task_log(task_id, "采集任务执行失败", "error")

                # 发布最终状态到Redis
                await self.publish_task_status(task_id)
                return

            elif is_youtube:
                # 使用YouTube任务执行器
                self.add_task_log(task_id, f"使用YouTube任务执行器 (平台ID: {platform_id})", "info")
                logger.info(f"任务{task_id}使用YouTube任务执行器，平台ID: {platform_id}")

                # 🔧 新增：初始化工作流引擎
                logger.info(f"🔧 开始初始化工作流引擎: task_id={task_id}")
                try:
                    # 确定工作流配置文件路径
                    metadata = task.get("metadata", {})
                    content_type = metadata.get("contentType", "") if isinstance(metadata, dict) else ""

                    if content_type == "shorts":
                        workflow_config_path = "config/platforms/youtube/workflows/shorts_upload.yaml"
                        workflow_name = "YouTube短视频上传"
                    else:
                        workflow_config_path = "config/platforms/youtube/workflows/video_upload.yaml"
                        workflow_name = "YouTube视频上传"

                    # 初始化工作流引擎
                    elements_config_path = "config/platforms/youtube/elements.yaml"
                    workflow_engine = WorkflowEngine(
                        elements_config_path=elements_config_path,
                        uploader=None,  # 将在YouTube执行器中设置
                        task_id=task_id
                    )

                    # 加载工作流配置
                    logger.info(f"🔧 开始加载工作流配置: {workflow_config_path}")
                    workflow_loaded = await workflow_engine.load_workflow(workflow_config_path)
                    if workflow_loaded:
                        logger.info(f"✅ 工作流引擎初始化成功: {workflow_name}")
                        self.add_task_log(task_id, f"工作流引擎初始化成功: {workflow_name}", "info")

                        # 立即保存工作流状态到后端
                        logger.info(f"🔧 立即保存工作流状态到后端")
                        await workflow_engine.save_workflow_state()
                        logger.info(f"✅ 工作流状态保存完成")

                        # 保存工作流引擎到任务数据中，供YouTube执行器使用
                        task["workflow_engine"] = workflow_engine
                        task["workflow_name"] = workflow_name
                        logger.info(f"🔧 工作流引擎已保存到任务数据: task_id='{workflow_engine.task_id}'")
                    else:
                        logger.error(f"❌ 工作流配置加载失败: {workflow_config_path}")
                        self.add_task_log(task_id, "工作流配置加载失败，使用传统执行方式", "warning")

                except Exception as workflow_error:
                    logger.error(f"工作流引擎初始化失败: {str(workflow_error)}")
                    self.add_task_log(task_id, f"工作流引擎初始化失败: {str(workflow_error)}", "warning")

                # 不覆盖平台ID，使用原始平台ID
                # task["platform_id"] = "youtube"

                # 调用YouTube任务执行器之前的调试信息
                logger.info(f"🔧 准备调用YouTube任务执行器: task_id={task_id}")
                logger.info(f"🔧 任务数据中是否有workflow_engine: {'workflow_engine' in task}")
                if 'workflow_engine' in task:
                    logger.info(f"🔧 调用前工作流引擎task_id: '{task['workflow_engine'].task_id}'")

                success = await self.youtube_task_executor.execute_youtube_upload_task(
                    task_id,
                    task,
                    self.add_task_log,
                    self.publish_task_status
                )

                # 根据执行结果更新任务状态
                if success and task.get("status") != "canceled":
                    task["status"] = "completed"
                    task["progress"] = 100
                    task["end_time"] = datetime.datetime.now().isoformat()  # 添加结束时间
                    self.add_task_log(task_id, "YouTube上传任务执行完成", "success")
                elif task.get("status") != "canceled" and task.get("status") != "completed":
                    task["status"] = "failed"
                    task["end_time"] = datetime.datetime.now().isoformat()  # 失败时也记录结束时间
                    self.add_task_log(task_id, "YouTube上传任务执行失败", "error")

                # 发布最终状态到Redis
                await self.publish_task_status(task_id)
                return

            # 默认任务执行逻辑（模拟）
            self.add_task_log(task_id, f"使用默认任务执行器，平台: {platform_id}", "info")

            # 模拟任务执行过程
            total_steps = 20
            for step in range(1, total_steps + 1):
                # 检查任务是否被取消或暂停
                if task.get("status") == "canceled":
                    logger.info(f"任务{task_id}已取消")
                    break

                if task.get("status") == "paused":
                    logger.info(f"任务{task_id}已暂停")
                    # 等待恢复
                    while task.get("status") == "paused":
                        await asyncio.sleep(1)

                    # 如果恢复后状态为canceled，则退出
                    if task.get("status") == "canceled":
                        logger.info(f"任务{task_id}已取消")
                        break

                # 更新进度
                progress = int(step / total_steps * 100)
                task["progress"] = progress

                # 随机添加日志
                if random.random() > 0.7:
                    messages = [
                        "正在处理文件...",
                        "上传内容中...",
                        "验证内容...",
                        "应用元数据...",
                        "等待服务器响应..."
                    ]
                    random_message = messages[random.randint(0, len(messages) - 1)]
                    self.add_task_log(task_id, random_message, "info")

                # 随机更新设备使用情况
                task["device_usage"] = {
                    "cpu": random.randint(40, 70),
                    "memory": random.randint(60, 80),
                    "network": "已连接"
                }

                # 发布任务状态到Redis
                await self.publish_task_status(task_id)

                # 模拟执行时间
                await asyncio.sleep(1.5)

            # 任务完成
            if task.get("status") != "canceled":
                task["status"] = "completed"
                task["progress"] = 100
                task["end_time"] = datetime.datetime.now().isoformat()  # 添加结束时间
                self.add_task_log(task_id, "任务执行完成", "success")

            # 发布最终状态到Redis
            await self.publish_task_status(task_id)

        except Exception as e:
            error_msg = str(e)
            logger.error(f"执行任务{task_id}异常: {error_msg}", exc_info=True)

            # 记录详细的错误信息
            detailed_error = {
                "error_type": type(e).__name__,
                "error_message": error_msg,
                "task_type": task.get("task_type", "unknown"),
                "device_id": task.get("device_id", "unknown"),
                "platform_id": task.get("platform_id", "unknown"),
                "content_path": task.get("content_path", "unknown")
            }

            # 添加详细的错误日志
            self.add_task_log(task_id, f"任务执行失败", "error")
            self.add_task_log(task_id, f"错误类型: {detailed_error['error_type']}", "error")
            self.add_task_log(task_id, f"错误信息: {detailed_error['error_message']}", "error")
            self.add_task_log(task_id, f"设备ID: {detailed_error['device_id']}", "info")
            self.add_task_log(task_id, f"平台: {detailed_error['platform_id']}", "info")

            # 如果是工作流执行错误，添加更多上下文
            if "workflow" in error_msg.lower() or "步骤" in error_msg:
                self.add_task_log(task_id, f"工作流执行过程中发生错误，请检查设备状态和网络连接", "warning")

            # 更新任务状态为失败，并保存错误信息
            if task_id in self.tasks:
                self.tasks[task_id]["status"] = "failed"
                self.tasks[task_id]["end_time"] = datetime.datetime.now().isoformat()
                self.tasks[task_id]["error_info"] = detailed_error  # 保存详细错误信息
                await self.publish_task_status(task_id)

        finally:
            # 移除正在运行的任务
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]

    async def execute_main_task(self, main_task_id: str, main_task: Dict[str, Any]) -> None:
        """执行主任务（管理子任务的执行）

        Args:
            main_task_id: 主任务ID
            main_task: 主任务数据
        """
        try:
            self.add_task_log(main_task_id, "开始执行主任务", "info")

            # 获取子任务列表
            total_subtasks = main_task.get("total_subtasks", 0)

            self.add_task_log(main_task_id, f"主任务包含 {total_subtasks} 个子任务", "info")

            # 主任务执行逻辑：
            # 1. 主任务本身不执行具体操作，只负责调度子任务
            # 2. 子任务由Backend通过API逐个启动
            # 3. Core服务只需要等待所有子任务完成

            self.add_task_log(main_task_id, "主任务已启动，等待子任务执行", "info")

            # 更新主任务状态
            main_task["status"] = "running"
            main_task["progress"] = 0
            await self.publish_task_status(main_task_id)

            # 主任务的执行逻辑：
            # - 主任务状态由Backend根据子任务完成情况更新
            # - Core服务不需要主动调度子任务，子任务由Backend API启动
            # - 这里只需要保持主任务运行状态

            self.add_task_log(main_task_id, "主任务调度完成，子任务将由系统自动执行", "success")

        except Exception as e:
            error_msg = str(e)
            logger.error(f"执行主任务{main_task_id}异常: {error_msg}", exc_info=True)

            # 记录详细的主任务错误信息
            self.add_task_log(main_task_id, f"主任务执行失败", "error")
            self.add_task_log(main_task_id, f"错误类型: {type(e).__name__}", "error")
            self.add_task_log(main_task_id, f"错误信息: {error_msg}", "error")

            # 检查子任务状态，提供更多上下文
            subtasks = main_task.get("subtasks", [])
            failed_subtasks = []
            for subtask in subtasks:
                if subtask.get("status") == "failed":
                    failed_subtasks.append(subtask.get("task_id", "unknown"))

            if failed_subtasks:
                self.add_task_log(main_task_id, f"失败的子任务: {', '.join(failed_subtasks)}", "warning")
                self.add_task_log(main_task_id, f"建议检查失败子任务的详细日志以了解具体原因", "info")

            # 更新主任务状态为失败，并保存错误信息
            main_task["status"] = "failed"
            main_task["end_time"] = datetime.datetime.now().isoformat()
            main_task["error_info"] = {
                "error_type": type(e).__name__,
                "error_message": error_msg,
                "failed_subtasks": failed_subtasks
            }
            await self.publish_task_status(main_task_id)

    async def pause_task(self, task_id: str) -> bool:
        """暂停任务

        Args:
            task_id: 任务ID

        Returns:
            bool: 是否成功
        """
        try:
            # 检查任务是否存在
            if task_id not in self.tasks:
                logger.error(f"任务{task_id}不存在")
                return False

            # 获取任务数据
            task = self.tasks[task_id]

            # 检查任务状态
            if task.get("status") != "running":
                logger.warning(f"任务{task_id}不在运行状态，无法暂停")
                return False

            # 更新任务状态
            task["status"] = "paused"

            # 添加日志
            self.add_task_log(task_id, "任务已暂停", "warning")

            # 发布任务状态到Redis
            await self.publish_task_status(task_id)

            logger.info(f"任务{task_id}已暂停")
            return True

        except Exception as e:
            logger.error(f"暂停任务异常: {str(e)}", exc_info=True)
            return False

    async def cancel_task(self, task_id: str) -> bool:
        """取消任务

        Args:
            task_id: 任务ID

        Returns:
            bool: 是否成功
        """
        try:
            # 检查任务是否存在
            if task_id not in self.tasks:
                logger.error(f"任务{task_id}不存在")
                return False

            # 获取任务数据
            task = self.tasks[task_id]

            # 检查任务状态
            if task.get("status") not in ["running", "paused", "pending"]:
                logger.warning(f"任务{task_id}状态为{task.get('status')}，无法取消")
                return False

            # 🔧 重要修复：如果任务正在运行，设置取消标志
            if task_id in self.running_tasks:
                logger.info(f"任务{task_id}正在运行，设置取消标志")
                # 设置取消标志，让正在运行的任务检查并停止
                task["cancel_requested"] = True

                # 等待一小段时间让任务自然停止
                import asyncio
                for i in range(10):  # 最多等待1秒
                    await asyncio.sleep(0.1)
                    if task_id not in self.running_tasks:
                        logger.info(f"任务{task_id}已自然停止")
                        break

                # 如果任务仍在运行，强制移除
                if task_id in self.running_tasks:
                    logger.warning(f"任务{task_id}未能自然停止，强制移除")
                    del self.running_tasks[task_id]

            # 更新任务状态
            task["status"] = "canceled"
            task["end_time"] = datetime.datetime.now().isoformat()  # 取消时也记录结束时间

            # 添加日志
            self.add_task_log(task_id, "任务已取消", "warning")

            # 发布任务状态到Redis
            await self.publish_task_status(task_id)

            logger.info(f"任务{task_id}已取消")
            return True

        except Exception as e:
            logger.error(f"取消任务异常: {str(e)}", exc_info=True)
            return False

    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            Optional[Dict[str, Any]]: 任务状态
        """
        try:
            # 检查任务是否存在
            if task_id not in self.tasks:
                logger.error(f"任务{task_id}不存在")
                return None

            # 获取任务数据
            task = self.tasks[task_id]

            # 构建状态数据
            status_data = {
                "task_id": task_id,
                "status": task.get("status", "unknown"),
                "progress": task.get("progress", 0),
                "start_time": task.get("start_time", ""),
                "estimated_end_time": task.get("estimated_end_time", ""),
                "device_usage": task.get("device_usage", {
                    "cpu": 0,
                    "memory": 0,
                    "network": "未知"
                }),
                "logs": self.get_recent_logs(task_id, 10),  # 获取最近10条日志
                "details": {
                    "platform_id": task.get("platform_id", ""),
                    "account_id": task.get("account_id", ""),
                    "device_id": task.get("device_id", ""),
                    "content_path": task.get("content_path", ""),
                    "created_at": task.get("created_at", "")
                },
                "message": self.get_status_message(task),
                "current_step": self.get_current_step(task),
                "steps": self.get_task_steps(task)
            }

            return status_data

        except Exception as e:
            logger.error(f"获取任务状态异常: {str(e)}", exc_info=True)
            return None

    async def get_task_logs(self, task_id: str) -> List[Dict[str, Any]]:
        """获取任务日志

        Args:
            task_id: 任务ID

        Returns:
            List[Dict[str, Any]]: 任务日志
        """
        try:
            # 检查任务是否存在
            if task_id not in self.task_logs:
                logger.error(f"任务{task_id}日志不存在")
                return []

            # 获取任务日志
            logs = self.task_logs[task_id]

            return logs

        except Exception as e:
            logger.error(f"获取任务日志异常: {str(e)}", exc_info=True)
            return []

    def add_task_log(self, task_id: str, message: str, level: str = "info") -> None:
        """添加任务日志

        Args:
            task_id: 任务ID
            message: 日志消息
            level: 日志级别
        """
        try:
            # 检查任务日志是否存在
            if task_id not in self.task_logs:
                self.task_logs[task_id] = []

            # 添加日志
            log_entry = {
                "message": message,
                "level": level,
                "timestamp": datetime.datetime.now().isoformat()
            }

            self.task_logs[task_id].append(log_entry)

            # 日志最多保留1000条
            if len(self.task_logs[task_id]) > 1000:
                self.task_logs[task_id] = self.task_logs[task_id][-1000:]

            # 记录到系统日志
            log_func = getattr(logger, level, logger.info)
            log_func(f"任务{task_id}: {message}")

        except Exception as e:
            logger.error(f"添加任务日志异常: {str(e)}", exc_info=True)

    def get_recent_logs(self, task_id: str, count: int = 10) -> List[Dict[str, Any]]:
        """获取最近的日志

        Args:
            task_id: 任务ID
            count: 日志数量

        Returns:
            List[Dict[str, Any]]: 最近的日志
        """
        try:
            # 检查任务日志是否存在
            if task_id not in self.task_logs:
                return []

            # 获取最近的日志
            logs = self.task_logs[task_id]

            return logs[-count:] if len(logs) > count else logs

        except Exception as e:
            logger.error(f"获取最近日志异常: {str(e)}", exc_info=True)
            return []

    def get_status_message(self, task: Dict[str, Any]) -> str:
        """获取任务状态消息

        Args:
            task: 任务数据

        Returns:
            str: 状态消息
        """
        status = task.get("status", "unknown")

        # 根据状态返回消息
        if status == "pending":
            return "任务已创建，等待执行"
        elif status == "running":
            progress = task.get("progress", 0)
            if progress < 20:
                return "正在连接设备..."
            elif progress < 40:
                return "正在启动应用..."
            elif progress < 60:
                return "正在处理内容..."
            elif progress < 80:
                return "正在上传内容..."
            else:
                return "正在完成任务..."
        elif status == "paused":
            return "任务已暂停，等待恢复"
        elif status == "completed":
            return "任务已成功完成"
        elif status == "failed":
            return "任务执行失败"
        elif status == "canceled":
            return "任务已取消"
        else:
            return "未知状态"

    def get_current_step(self, task: Dict[str, Any]) -> int:
        """获取当前步骤

        Args:
            task: 任务数据

        Returns:
            int: 当前步骤
        """
        status = task.get("status", "unknown")
        progress = task.get("progress", 0)

        # 根据状态和进度返回当前步骤
        if status == "pending":
            return 1
        elif status == "running":
            if progress < 20:
                return 2  # 连接设备
            elif progress < 40:
                return 3  # 启动应用
            elif progress < 80:
                return 4  # 执行操作
            else:
                return 5  # 完成任务
        elif status == "completed":
            return 5
        elif status == "failed" or status == "canceled":
            # 如果失败或取消，返回最后一个进行中的步骤
            if progress < 20:
                return 2
            elif progress < 40:
                return 3
            elif progress < 80:
                return 4
            else:
                return 5
        else:
            return 1

    def get_task_steps(self, task: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取任务步骤

        Args:
            task: 任务数据

        Returns:
            List[Dict[str, Any]]: 任务步骤
        """
        status = task.get("status", "unknown")
        progress = task.get("progress", 0)
        current_step = self.get_current_step(task)

        # 初始化步骤
        steps = [
            {"step": 1, "name": "准备任务", "status": "success", "message": "任务准备就绪"},
            {"step": 2, "name": "连接设备", "status": "wait", "message": "等待任务开始"},
            {"step": 3, "name": "启动应用", "status": "wait", "message": "等待设备连接完成"},
            {"step": 4, "name": "执行操作", "status": "wait", "message": "等待应用启动完成"},
            {"step": 5, "name": "完成任务", "status": "wait", "message": "等待操作执行完成"}
        ]

        # 更新步骤状态
        for i in range(5):
            step_num = i + 1

            if step_num < current_step:
                # 之前的步骤已完成
                steps[i]["status"] = "success"

                # 更新消息
                if step_num == 1:
                    steps[i]["message"] = "任务准备就绪"
                elif step_num == 2:
                    steps[i]["message"] = "设备连接成功"
                elif step_num == 3:
                    steps[i]["message"] = "应用启动成功"
                elif step_num == 4:
                    steps[i]["message"] = "操作执行成功"

            elif step_num == current_step:
                # 当前步骤
                if status == "running":
                    steps[i]["status"] = "process"

                    # 更新消息
                    if step_num == 2:
                        steps[i]["message"] = "正在连接设备..."
                    elif step_num == 3:
                        steps[i]["message"] = "正在启动应用..."
                    elif step_num == 4:
                        if progress < 60:
                            steps[i]["message"] = "正在处理内容..."
                        else:
                            steps[i]["message"] = "正在上传内容..."
                    elif step_num == 5:
                        steps[i]["message"] = "正在完成任务..."

                elif status == "paused":
                    steps[i]["status"] = "warning"
                    steps[i]["message"] = "已暂停"
                elif status == "failed":
                    steps[i]["status"] = "error"
                    steps[i]["message"] = "执行失败"
                elif status == "canceled":
                    steps[i]["status"] = "warning"
                    steps[i]["message"] = "已取消"
                elif status == "completed" and step_num == 5:
                    steps[i]["status"] = "success"
                    steps[i]["message"] = "任务已完成"

        return steps

    async def publish_task_status(self, task_id: str) -> None:
        """发布任务状态到Redis

        Args:
            task_id: 任务ID
        """
        try:
            # 检查Redis客户端是否可用
            if not self.redis_client:
                # 尝试重新连接Redis
                await self._reconnect_redis()

                # 如果重连后仍然不可用，则使用内存存储
                if not self.redis_client:
                    logger.warning(f"Redis客户端不可用，无法发布任务{task_id}状态")
                    # 保存到内存中
                    self._save_task_status_to_memory(task_id)
                    return

            # 获取任务状态
            status = await self.get_task_status(task_id)
            if not status:
                logger.warning(f"获取任务{task_id}状态失败，无法发布状态")
                return

            # 发布到Redis
            channel = f"task:{task_id}:status"
            status_json = json.dumps(status)

            # 记录详细日志
            logger.info(f"发布任务{task_id}状态到Redis，channel: {channel}")
            logger.debug(f"任务{task_id}状态数据: {status_json[:200]}...")

            try:
                # 发布状态
                publish_result = await self.redis_client.publish(channel, status_json)
                logger.info(f"Redis发布结果: {publish_result}个客户端接收到消息")

                # 同时保存最新状态
                key = f"task:{task_id}:latest"
                await self.redis_client.set(key, status_json)

                # 设置过期时间（24小时）
                await self.redis_client.expire(key, 86400)

                # 确认状态已保存
                saved_status = await self.redis_client.get(key)
                if saved_status:
                    logger.info(f"任务{task_id}状态已保存到Redis，key: {key}")
                else:
                    logger.warning(f"任务{task_id}状态保存失败")
            except Exception as redis_error:
                logger.error(f"Redis操作失败: {str(redis_error)}", exc_info=True)
                # 尝试重新连接
                await self._reconnect_redis()
                # 保存到内存中
                self._save_task_status_to_memory(task_id)

        except Exception as e:
            logger.error(f"发布任务{task_id}状态异常: {str(e)}", exc_info=True)
            # 尝试重新连接Redis
            await self._reconnect_redis()

    async def _reconnect_redis(self) -> bool:
        """尝试重新连接Redis

        Returns:
            bool: 是否成功重连
        """
        try:
            # 首先尝试从main_service.settings获取
            redis_url = None
            if hasattr(self.main_service, 'settings') and self.main_service.settings and hasattr(self.main_service.settings, 'redis_url'):
                redis_url = self.main_service.settings.redis_url
                logger.info(f"从settings获取Redis URL: {redis_url}")

            # 如果无法从settings获取，尝试从环境变量获取
            if not redis_url:
                import os
                redis_url = os.environ.get("REDIS_URL")
                if redis_url:
                    logger.info(f"从环境变量获取Redis URL: {redis_url}")

            # 如果仍然无法获取，使用硬编码的默认值
            if not redis_url:
                # 使用与设备同步服务相同的默认值
                redis_url = "redis://192.168.123.137:6379/1"
                logger.info(f"使用默认Redis URL: {redis_url}")

            # 使用与设备同步服务相同的连接方式
            try:
                # 创建Redis客户端并注册到资源管理器
                self.redis_client = resource_manager.register_redis_client(
                    redis.from_url(redis_url)
                )

                # 测试连接
                await self.redis_client.ping()
                logger.info(f"Redis连接成功: {redis_url}")
                return True
            except Exception as redis_error:
                logger.error(f"Redis连接失败: {str(redis_error)}", exc_info=True)
                self.redis_client = None
                return False
        except Exception as reconnect_error:
            logger.error(f"重新连接Redis失败: {str(reconnect_error)}", exc_info=True)
            self.redis_client = None
            return False

    def _save_task_status_to_memory(self, task_id: str) -> None:
        """将任务状态保存到内存中

        Args:
            task_id: 任务ID
        """
        # 这里可以实现内存存储逻辑，例如保存到文件或内存缓存
        logger.info(f"任务{task_id}状态已保存到内存中")
        # 可以在这里实现备用存储机制，如保存到本地文件

    async def shutdown(self) -> None:
        """关闭任务执行器"""
        try:
            logger.info("正在关闭任务执行器...")

            # 取消所有正在运行的任务
            running_tasks = list(self.running_tasks.keys())
            for task_id in running_tasks:
                try:
                    logger.info(f"正在取消任务: {task_id}")
                    await self.cancel_task(task_id)
                except Exception as task_error:
                    logger.error(f"取消任务 {task_id} 失败: {str(task_error)}")

            # 关闭Redis连接
            if self.redis_client:
                try:
                    await self.redis_client.close()
                    logger.info("Redis连接已关闭")
                except Exception as redis_error:
                    logger.error(f"关闭Redis连接失败: {str(redis_error)}")

            self.redis_client = None
            self.is_initialized = False
            logger.info("任务执行器已关闭")

        except Exception as e:
            logger.error(f"关闭任务执行器异常: {str(e)}", exc_info=True)
