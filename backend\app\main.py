import os
import yaml
import logging
from typing import List, Optional
from fastapi import FastAP<PERSON>, Request
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
from socketio import AsyncServer, ASGIApp
from fastapi.middleware.cors import CORSMiddleware
from motor.motor_asyncio import AsyncIOMotorClient
import redis
import consul
import jwt
import asyncio
from dotenv import load_dotenv
from app.api import auth, device, social, core, music, task, video_collect, content_collect
from app.api.v1 import filesystem, youtube_upload, social_accounts, social_publish, social_platforms, platform_apps, device_accounts, content_manager, download_tasks, categories, benchmark_accounts, benchmark_download, task_scheduler, proxy_management, workflow, content_generate
from app.core.client import DeviceServiceClient
from app.config.database import DatabaseConfig
from app.services.redis_sync_service import RedisSyncService

# 🔧 新的日志配置 - 按日期和类型分文件
from app.config.logging_config import setup_backend_logging, get_logger

# 设置日志配置
setup_backend_logging("logs")
logger = get_logger(__name__)

# 加载环境变量
load_dotenv()
ENVIRONMENT = os.getenv("ENVIRONMENT", "development")
CORS_CONFIG_PATH = os.getenv("CORS_CONFIG_PATH", "cors_config.yaml")

# CORS 配置模型
class CORSConfig(BaseModel):
    allow_origins: List[str]
    allow_credentials: bool = True
    allow_methods: List[str] = ["*"]
    allow_headers: List[str] = ["*"]  # 恢复原始配置，允许所有头
    expose_headers: List[str] = ["*"]
    allow_origin_regex: Optional[str] = None

# CORS 管理类
class CORSManager:
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.configs = self.load_configs()

    def load_configs(self) -> dict:
        """从 YAML 文件加载 CORS 配置"""
        try:
            with open(self.config_path, 'r') as f:
                configs = yaml.safe_load(f)
            return {env: CORSConfig(**config) for env, config in configs.items()}
        except Exception as e:
            logger.error(f"无法加载 CORS 配置文件: {e}")
            # 默认配置
            return {
                "development": CORSConfig(
                    allow_origins=[
                        "http://localhost:5173",
                        "http://127.0.0.1:5173",
                        "http://*************:5173",
                        "ws://localhost:5173",
                        "ws://127.0.0.1:5173",
                        "ws://*************:5173",
                    ],
                    allow_origin_regex=r"https?://(localhost|127\.0\.0\.1|192\.168\.123\.2)(:\d+)?|ws://(localhost|127\.0\.0\.1|192\.168\.123\.2)(:\d+)?",
                ),
                "production": CORSConfig(
                    allow_origins=["https://your-production-domain.com"],
                    allow_methods=["GET", "POST", "OPTIONS"],
                    allow_headers=["*"],
                    expose_headers=["*"],
                ),
            }

    def get_config(self, environment: str) -> CORSConfig:
        """获取指定环境的 CORS 配置"""
        return self.configs.get(environment, self.configs["development"])

# 获取 CORS 配置
def get_cors_config() -> CORSConfig:
    manager = CORSManager(CORS_CONFIG_PATH)
    return manager.get_config(ENVIRONMENT)

# 创建 FastAPI 应用
app = FastAPI()

# 添加 CORS 中间件
# 添加 CORS 中间件
cors_config = get_cors_config()
app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_config.allow_origins,
    allow_credentials=True,
    allow_methods=cors_config.allow_methods,
    allow_headers=cors_config.allow_headers,
    expose_headers=cors_config.expose_headers,
    allow_origin_regex=cors_config.allow_origin_regex,
)

# 添加调试日志
@app.middleware("http")
async def debug_cors(request: Request, call_next):
    logger.debug(f"收到请求: {request.method} {request.url}")
    logger.debug(f"请求头 Origin: {request.headers.get('origin')}")
    logger.debug(f"所有请求头: {dict(request.headers)}")

    try:
        response = await call_next(request)
        logger.debug(f"响应状态码: {response.status_code}")
        logger.debug(f"响应头: {response.headers}")
        return response
    except Exception as e:
        logger.error(f"处理请求时发生错误: {str(e)}", exc_info=True)
        raise

# 创建 Socket.IO 服务器
def create_socketio_server(cors_config: CORSConfig) -> AsyncServer:
    return AsyncServer(
        async_mode='asgi',
        cors_allowed_origins=cors_config.allow_origins,
        logger=ENVIRONMENT == "development",
        engineio_logger=False,
        async_handlers=True,
    )

device_sio = create_socketio_server(cors_config)
auth_sio = create_socketio_server(cors_config)

# 挂载 Socket.IO
app.mount("/device-socket.io", ASGIApp(device_sio, socketio_path=""))
app.mount("/auth-socket.io", ASGIApp(auth_sio, socketio_path=""))

@app.on_event("startup")
async def startup():
    logger.info("服务器启动，启用 CORS")
    # 初始化 MongoDB 连接
    db_config = DatabaseConfig()
    app.state.mongo_client = AsyncIOMotorClient(db_config.mongodb_url)
    app.state.mongo_db = app.state.mongo_client[db_config.mongodb_name]
    logger.info("MongoDB 已连接")

    # Redis 连接
    app.state.redis_client = redis.Redis.from_url(db_config.redis_url)
    logger.info("Redis 已连接")

    # Consul 连接
    from urllib.parse import urlparse
    consul_url = urlparse(db_config.consul_url)
    consul_host = consul_url.hostname or "localhost"
    consul_port = consul_url.port or 8500
    app.state.consul_client = consul.Consul(host=consul_host, port=consul_port)
    logger.info(f"Consul 已连接，地址: {consul_host}:{consul_port}")

    # 初始化设备服务 - 尝试从 Consul 发现 Core 服务
    try:
        logger.info("尝试从 Consul 发现 Core 服务")
        # 查询 Consul 中的 Core 服务
        services = app.state.consul_client.health.service('thunderhub-core', passing=True)[1]

        if services:
            # 使用第一个健康的 Core 服务
            service = services[0]
            core_host = service['Service']['Address'] or service['Node']['Address']
            core_port = service['Service']['Port']
            logger.info(f"从 Consul 发现 Core 服务: {core_host}:{core_port}")
            device_client = DeviceServiceClient(host=core_host, port=core_port)
        else:
            logger.warning("Consul 中未找到 Core 服务，使用模拟服务")
            # 创建模拟设备服务
            device_client = None
            logger.info("应用状态中没有设备服务，创建模拟服务")
    except Exception as e:
        logger.error(f"从 Consul 发现 Core 服务失败: {str(e)}")
        logger.warning("使用模拟设备服务")
        device_client = None

    app.state.device_service = device_client
    logger.info("设备服务初始化完成")

    # 启动Redis同步服务
    # 根据新架构，设备状态由Core推送到Redis，后端从Redis获取并推送给前端
    try:
        # 为Redis同步服务创建单独的同步MongoDB客户端
        from pymongo import MongoClient
        sync_mongo_client = MongoClient(db_config.mongodb_url)
        sync_mongo_db = sync_mongo_client[db_config.mongodb_name]

        redis_sync_service = RedisSyncService(
            redis_url=db_config.redis_url,
            socketio=device_sio,
            mongo_db=sync_mongo_db,  # 使用同步客户端
            snapshot_interval=300  # 5分钟保存一次快照
        )
        await redis_sync_service.start()
        app.state.redis_sync_service = redis_sync_service
        app.state.sync_mongo_client = sync_mongo_client  # 保存同步客户端引用
        logger.info("Redis同步服务已启动")
    except Exception as e:
        logger.error(f"启动Redis同步服务失败: {str(e)}", exc_info=True)

# 认证 Socket.IO 事件
@auth_sio.event
async def connect(sid, environ, auth=None):
    logger.debug(f"认证连接尝试: {sid}, 路径: {environ.get('PATH_INFO')}")
    logger.debug(f"认证参数: {auth}")
    logger.debug(f"环境变量键: {list(environ.keys())}")

    handshake = auth_sio.get_environ(sid, 'handshake')
    if handshake:
        logger.debug(f"认证握手数据键: {list(handshake.keys())}")
        logger.debug(f"握手数据内容: {handshake}")
    else:
        logger.warning(f"会话 {sid} 的握手数据为空，尝试其他认证方式")

    token = ''

    # 1. 优先从 auth 参数获取 token
    if auth and isinstance(auth, dict) and auth.get('token'):
        token = auth['token']
        # 移除 Bearer 前缀（如果存在）
        if token.startswith('Bearer '):
            token = token.replace('Bearer ', '')
        logger.debug("从显式 auth 参数获取 token")

    # 2. 从握手数据获取 token
    elif handshake and handshake.get('auth', {}).get('token'):
        token = handshake['auth']['token']
        if token.startswith('Bearer '):
            token = token.replace('Bearer ', '')
        logger.debug("从 handshake.auth 获取 token")

    # 3. 从 HTTP 头获取 token
    elif 'HTTP_AUTHORIZATION' in environ and 'Bearer ' in environ['HTTP_AUTHORIZATION']:
        token = environ['HTTP_AUTHORIZATION'].replace('Bearer ', '')
        logger.debug("从 HTTP 头获取 token")

    # 4. 从查询字符串获取 token
    elif 'QUERY_STRING' in environ and 'token=' in environ['QUERY_STRING']:
        query_string = environ['QUERY_STRING']
        for param in query_string.split('&'):
            if param.startswith('token='):
                token = param.split('=', 1)[1]
                logger.debug("从查询字符串获取 token")
                break

    # 检查 token 是否为空或无效
    if not token or token.strip() == '':
        logger.warning(f"未找到有效 token，auth={auth}, environ keys={list(environ.keys())}")
        raise ConnectionRefusedError('需要认证')

    try:
        decoded = jwt.decode(token, options={"verify_signature": False})
        logger.debug(f"Token 信息 - iss: {decoded.get('iss')}, sub: {decoded.get('sub')}, exp: {decoded.get('exp')}")
    except Exception as e:
        logger.warning(f"Token 解码错误: {str(e)}")
        raise ConnectionRefusedError('Token 无效')

    logger.debug(f"使用 token: {'****'+token[-4:] if len(token) >= 4 else '****'}")
    logger.debug(f"认证客户端已连接: {sid}")

@auth_sio.event
async def disconnect(sid):
    logger.debug(f"认证客户端断开连接: {sid}")

# 设备 Socket.IO 事件
@device_sio.event
async def connect(sid, environ, auth=None):
    logger.debug(f"设备连接尝试: {sid}, 路径: {environ.get('PATH_INFO')}")
    logger.debug(f"设备认证参数: {auth}")
    logger.debug(f"设备环境变量键: {list(environ.keys())}")

    handshake = device_sio.get_environ(sid, 'handshake')
    if handshake:
        logger.debug(f"设备握手数据键: {list(handshake.keys())}")
        logger.debug(f"设备握手数据内容: {handshake}")

    token = ''

    # 1. 优先从 auth 参数获取 token
    if auth and isinstance(auth, dict) and auth.get('token'):
        token = auth['token']
        # 移除 Bearer 前缀（如果存在）
        if token.startswith('Bearer '):
            token = token.replace('Bearer ', '')
        logger.debug("从显式 auth 参数获取 token")

    # 2. 从 auth 字符串获取 token
    elif auth and isinstance(auth, str):
        token = auth
        if token.startswith('Bearer '):
            token = token.replace('Bearer ', '')
        logger.debug("从 auth 字符串获取 token")

    # 3. 从握手数据获取 token
    elif handshake and handshake.get('auth', {}).get('token'):
        token = handshake['auth']['token']
        if token.startswith('Bearer '):
            token = token.replace('Bearer ', '')
        logger.debug("从 handshake.auth 获取 token")

    # 4. 从 HTTP 头获取 token
    elif 'HTTP_AUTHORIZATION' in environ and 'Bearer ' in environ['HTTP_AUTHORIZATION']:
        token = environ['HTTP_AUTHORIZATION'].replace('Bearer ', '')
        logger.debug("从 HTTP 头获取 token")

    # 5. 从查询字符串获取 token
    elif 'QUERY_STRING' in environ and 'token=' in environ['QUERY_STRING']:
        query_string = environ['QUERY_STRING']
        for param in query_string.split('&'):
            if param.startswith('token='):
                token = param.split('=', 1)[1]
                logger.debug("从查询字符串获取 token")
                break

    # 6. 从 headers 字典获取 token
    elif 'headers' in environ and environ['headers'].get('Authorization'):
        token = environ['headers']['Authorization']
        if token.startswith('Bearer '):
            token = token.replace('Bearer ', '')
        logger.debug("从 headers 字典获取 token")

    # 检查 token 是否为空或无效
    if not token or token.strip() == '':
        logger.warning(f"设备连接未找到有效 token，auth={auth}, environ keys={list(environ.keys())}")
        raise ConnectionRefusedError('需要认证')

    try:
        decoded = jwt.decode(token, options={"verify_signature": False})
        logger.debug(f"设备Token 信息 - iss: {decoded.get('iss')}, sub: {decoded.get('sub')}, exp: {decoded.get('exp')}")
    except Exception as e:
        logger.warning(f"设备Token 解码错误: {str(e)}")
        raise ConnectionRefusedError('Token 无效')

    logger.debug(f"设备使用 token: {'****'+token[-4:] if len(token) >= 4 else '****'}")
    logger.debug(f"设备客户端已连接: {sid}")

@device_sio.event
async def disconnect(sid):
    logger.debug(f"设备客户端断开连接: {sid}")

# 路由注册
app.include_router(auth.router)
app.include_router(device.init_device_routes(app, device_sio))
app.include_router(social.init_social_routes(app, device_sio))
app.include_router(task.init_task_routes())  # 注册任务管理路由
app.include_router(music.router, prefix="/api/music", tags=["音乐库"])  # 注册音乐库路由
app.include_router(filesystem.router)
app.include_router(youtube_upload.router)
app.include_router(social_accounts.router)
app.include_router(social_platforms.router)  # 注册社交平台v1路由
app.include_router(platform_apps.router)  # 注册平台应用v1路由
app.include_router(social_publish.router)
app.include_router(device_accounts.router)  # 注册设备账号关联v1路由
app.include_router(content_manager.router)  # 注册内容管理路由
app.include_router(download_tasks.router)  # 注册下载任务路由
app.include_router(categories.router)  # 注册分类管理路由
app.include_router(benchmark_accounts.router)  # 注册对标账号管理路由
app.include_router(benchmark_download.router)  # 注册对标账号下载路由
app.include_router(video_collect.router)  # 注册视频采集路由
app.include_router(content_collect.router)  # 注册内容采集路由
app.include_router(task_scheduler.router, prefix="/api/v1/task-scheduler", tags=["任务调度"])  # 注册任务调度路由
app.include_router(proxy_management.router)  # 注册代理IP管理路由
app.include_router(workflow.router, prefix="/api/tasks", tags=["工作流管理"])  # 注册工作流管理路由
app.include_router(content_generate.router)  # 注册内容生成路由
core.init_core_routes(app)  # 注册Core服务路由

@app.get("/")
async def root():
    return {"message": "欢迎使用 ThunderHub API"}

@app.on_event("shutdown")
async def shutdown():
    logger.info("服务器关闭")

    # 关闭Redis同步服务
    if hasattr(app.state, 'redis_sync_service'):
        await app.state.redis_sync_service.stop()
        logger.info("Redis同步服务已关闭")

    # 关闭MongoDB连接
    if hasattr(app.state, 'mongo_client'):
        app.state.mongo_client.close()
        logger.info("MongoDB 连接已关闭")

    logger.info("所有连接已关闭")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="debug"
    )