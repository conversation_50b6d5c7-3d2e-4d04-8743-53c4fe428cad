/**
 * 文件系统API接口
 */

import request from '@/utils/request'

// 数据类型定义
export interface MediaInfo {
  duration: number
  resolution: string
  video_codec: string
  audio_codec: string
  frame_rate: number
  bitrate: number
  video?: {
    codec: string
    width: number
    height: number
    fps: number
  }
  audio?: {
    codec: string
    sample_rate: number
    channels: number
  }
}

export interface FileInfo {
  name: string
  path: string
  size: number
  is_directory: boolean
  extension?: string
  last_modified: string
  md5_hash?: string
  is_uploaded?: boolean
  media_info?: MediaInfo
}

export interface FolderListResponse {
  path: string
  files: FileInfo[]
  parent_path?: string
}

// 获取文件夹内容
export const getFolderContents = (params: {
  path: string
  filter_extensions?: string[]
  include_md5?: boolean
  include_media_info?: boolean
  core_service_id?: string
}) => {
  return request<FolderListResponse>({
    url: '/api/v1/filesystem/list',
    method: 'post',
    data: {
      path: params.path
    },
    params: {
      filter_extensions: params.filter_extensions,
      include_md5: params.include_md5,
      include_media_info: params.include_media_info,
      core_service_id: params.core_service_id
    }
  })
}

// 获取视频文件列表（从指定文件夹）
export const getVideoFiles = (folderPath: string) => {
  return getFolderContents({
    path: folderPath,
    filter_extensions: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'],
    include_md5: true,
    include_media_info: true
  })
}

// 获取文件路径配置
export const getFilePaths = () => {
  return request({
    url: '/api/v1/filesystem/paths',
    method: 'get'
  })
}

// 检查路径是否存在
export const checkPathExists = (path: string) => {
  return request({
    url: '/api/v1/filesystem/check-path',
    method: 'post',
    data: { path }
  })
}

// 验证视频文件夹
export const validateVideoFolder = (path: string) => {
  return request({
    url: '/api/v1/filesystem/validate-video-folder',
    method: 'post',
    data: { path }
  })
}

// 获取视频预览信息
export const getVideoPreviewInfo = (videoPath: string, includeThumbnail = false) => {
  return request({
    url: '/api/v1/filesystem/video-preview-info',
    method: 'post',
    data: {
      video_path: videoPath,
      include_thumbnail: includeThumbnail,
      include_detailed_metadata: true
    }
  })
}

// 视频裁剪相关接口
export interface VideoClipRequest {
  video_paths: string[]
  clip_mode: 'segments' | 'duration'  // 裁剪模式
  segment_count?: number  // 段数（segments模式）
  segment_duration?: number  // 每段时长（duration模式）
  buffer_duration?: number  // 裁剪点检测缓冲区
  output_quality?: 'high' | 'medium' | 'low'  // 输出质量
  output_folder?: string  // 输出文件夹
  filename_template?: string  // 文件名模板
  preserve_audio_quality?: boolean  // 保留原始音频质量
  max_concurrent?: number  // 最大并发数
  volume_sensitivity?: number  // 音量检测敏感度 (0.0-1.0)
  min_segment_duration?: number  // 最小片段时长
  background_processing?: boolean  // 是否后台处理
}

export interface VideoSegmentInfo {
  file_path: string
  segment_index: number
  start_time: number
  end_time: number
  duration: number
  file_size: number
  start_volume: number
  end_volume: number
}

export interface VideoClipResult {
  input_path: string
  success: boolean
  error_message: string
  processing_time_ms: number
  segments: VideoSegmentInfo[]
  original_duration: number
  volume_analysis_data: string
}

export interface VideoClipResponse {
  success: boolean
  error: string
  results?: VideoClipResult[]
  total_processing_time_ms?: number
  successful_count?: number
  failed_count?: number
  total_count?: number
  output_folder?: string
  total_output_size?: number
  total_segments?: number
  // 后台处理相关字段
  task_id?: string  // 后台任务ID
  message?: string  // 提示信息
}

// 智能视频裁剪
export const clipVideos = (data: VideoClipRequest, coreServiceId?: string) => {
  return request({
    url: '/api/v1/filesystem/clip-videos',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}

// 查询视频裁剪任务状态
export const getVideoClipTaskStatus = (taskId: string) => {
  return request({
    url: `/api/v1/filesystem/clip-videos/task/${taskId}`,
    method: 'get'
  })
}

// 视频加速相关接口
export interface VideoAccelerationRequest {
  video_paths: string[]
  target_duration: number  // 目标时长（秒）
  output_quality: 'high' | 'medium' | 'low'  // 输出质量
  overwrite_original: boolean  // 是否覆盖原文件
  output_suffix: string  // 输出文件名后缀
  background_processing?: boolean  // 是否后台处理
}

export interface VideoAccelerationResult {
  original_path: string
  output_path: string
  success: boolean
  error_message?: string
  processing_time_ms: number
  original_file_size: number
  output_file_size: number
  original_duration: number
  output_duration: number
  speed_factor: number
}

export interface VideoAccelerationResponse {
  success: boolean
  error?: string
  results?: VideoAccelerationResult[]
  successful_count?: number
  failed_count?: number
  total_processing_time_ms?: number
  // 后台处理相关字段
  task_id?: string  // 后台任务ID
  message?: string  // 提示信息
}

// 视频加速
export const accelerateVideos = (data: VideoAccelerationRequest, coreServiceId?: string) => {
  return request({
    url: '/api/v1/filesystem/accelerate-videos',
    method: 'post',
    data,
    params: coreServiceId ? { core_service_id: coreServiceId } : {}
  })
}

// 查询视频加速任务状态
export const getVideoAccelerationTaskStatus = (taskId: string) => {
  return request({
    url: `/api/v1/filesystem/accelerate-videos/task/${taskId}`,
    method: 'get'
  })
}
