import os
import json
from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.encoders import jsonable_encoder
from app.core.schemas.device_repository import DeviceRepository

import httpx
from typing import List, Optional, Dict, Any
import asyncio
import datetime
import logging
from bson import ObjectId

# 配置日志
logger = logging.getLogger(__name__)

from app.core.security import get_current_user


async def get_device_proxy_info(device_id: str) -> Optional[Dict[str, Any]]:
    """获取设备的代理IP关联信息

    Args:
        device_id: 设备ID

    Returns:
        代理IP信息字典，如果没有关联则返回None
    """
    try:
        # 获取数据库连接
        from app.core.database import get_db_service
        db_service = get_db_service()
        db = db_service.db

        # 查询设备代理关联
        mapping = await db.device_proxy_mappings.find_one({
            "device_id": device_id,
            "status": "active"
        })

        if not mapping:
            return None

        # 获取代理IP详细信息
        proxy_id = mapping.get("proxy_id")
        if not proxy_id:
            return None

        proxy = await db.proxy_ips.find_one({"_id": ObjectId(proxy_id)})
        if not proxy:
            return None

        return {
            "id": str(proxy["_id"]),
            "region": proxy.get("region", ""),
            "ip_address": proxy.get("ip_address", ""),
            "port": proxy.get("port", 0),
            "proxy_type": proxy.get("proxy_type", ""),
            "status": proxy.get("status", "")
        }

    except Exception as e:
        logger.error(f"获取设备代理信息失败: {str(e)}")
        return None



async def get_device_repo(request: Request):
    """获取设备仓库实例

    Args:
        request: FastAPI请求对象，由依赖注入系统自动提供

    Returns:
        DeviceRepository: 设备仓库实例
    """
    return DeviceRepository(request.app.state.mongo_db)

async def get_device_service(request: Request = None):
    """通过API获取设备管理服务

    Args:
        request: FastAPI请求对象，可选

    Returns:
        设备管理服务实例
    """
    # 如果提供了请求对象，尝试从应用状态获取设备服务
    if request and hasattr(request.app.state, 'device_service'):
        return request.app.state.device_service

    # 否则尝试通过API获取
    logger = logging.getLogger(__name__)
    logger.debug("尝试通过API获取设备服务")

    try:
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get("http://localhost:8000/api/v1/devices/service")
                response.raise_for_status()
                return response.json()
            except httpx.HTTPStatusError as e:
                logger.error(f"获取设备服务失败: {e}")
                raise HTTPException(
                    status_code=502,
                    detail="无法连接到设备管理服务"
                )
    except Exception as e:
        logger.error(f"获取设备服务异常: {str(e)}", exc_info=True)
        # 返回一个模拟服务对象，避免整个应用崩溃
        return {
            "get_running_list": lambda: [],
            "sort_windows": lambda: False
        }

def init_device_routes(fastapi_app, sio):
    # 获取当前模块的日志记录器
    logger = logging.getLogger(__name__)

    router = APIRouter(
        prefix="/api/devices",
        tags=["devices"],
        dependencies=[Depends(get_current_user)]
    )

    # 设备同步API端点 - 不需要认证，供Core服务调用
    sync_router = APIRouter(
        prefix="/api/v1/devices",
        tags=["device-sync"]
    )

    @router.get("/running", response_model=List[str])
    async def get_running_devices():
        """获取运行中的设备列表"""
        try:
            return await fastapi_app.state.device_service.get_running_list()
        except Exception as e:
            logger.error(f"获取运行设备失败: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail="获取运行设备列表失败"
            )

    # 初始化默认设备
    # 注意：这里不传递请求对象，因为在路由初始化时没有请求上下文
    # 设备服务将在实际请求时通过依赖注入获取
    try:
        logger.info("尝试初始化设备服务")
        # 检查应用状态中是否有设备服务
        if hasattr(fastapi_app.state, 'device_service'):
            service = fastapi_app.state.device_service
            logger.info(f"使用应用状态中的设备服务: {type(service).__name__ if service else 'None'}")
        else:
            # 创建一个模拟服务
            logger.info("应用状态中没有设备服务，创建模拟服务")
            # 设置一个默认的设备服务
            fastapi_app.state.device_service = {
                "get_running_list": lambda: [],
                "sort_windows": lambda: False,
                "start_device": lambda device_id: False,
                "stop_device": lambda device_id: False,
                "batch_operation": lambda op, device_ids: {did: False for did in device_ids},
                "get_device_status": lambda device_id: {
                    "id": device_id,
                    "name": f"设备-{device_id}",
                    "status": "unknown",
                    "type": "unknown",
                    "cpu": "0%",
                    "memory": "0%",
                    "network": "unknown",
                    "display_info": {
                        "width": 1080,
                        "height": 1920,
                        "dpi": 320
                    }
                }
            }
    except Exception as e:
        logger.error(f"初始化设备服务失败: {str(e)}", exc_info=True)
        # 不抛出异常，让应用继续启动

    # 配置Socket.IO参数（如果sio存在）
    if sio is not None:
        sio.eio.ping_interval = 60  # ping间隔从25秒增加到60秒
        sio.eio.ping_timeout = 180  # ping超时从60秒增加到180秒
        # 保存socket.io实例到fastapi_app
        fastapi_app.sio = sio

    # 确保socket.io实例有效
    if not sio:
        logger.error("Device Socket.IO instance is required")
        raise ValueError("Device Socket.IO instance is required")

    # 心跳检测事件
    @sio.on('ping')
    async def handle_ping(sid):
        await sio.emit('pong', room=sid)

    # 任务状态订阅
    @sio.on('subscribe_task')
    async def handle_subscribe_task(sid, task_id):
        """订阅任务状态更新"""
        logger.info(f"客户端 {sid} 订阅任务 {task_id} 状态")
        # 将客户端加入任务特定的房间
        await sio.enter_room(sid, f"task_{task_id}")

        # 尝试立即获取并发送任务状态
        try:
            # 首先尝试从Redis获取最新状态
            if hasattr(fastapi_app.state, 'redis'):
                redis_client = fastapi_app.state.redis
                key = f"task:{task_id}:latest"

                # 从Redis获取最新状态
                latest_status = await redis_client.get(key)

                if latest_status:
                    try:
                        status_data = json.loads(latest_status)
                        # 发送状态更新
                        await sio.emit("task_status_update", status_data, room=sid)
                        logger.info(f"已从Redis向客户端 {sid} 发送任务 {task_id} 的最新状态")
                        return
                    except json.JSONDecodeError:
                        logger.error(f"解析Redis中的任务 {task_id} 状态JSON失败")

            # 如果Redis中没有状态，从MongoDB获取
            if hasattr(fastapi_app.state, 'mongo_db'):
                db = fastapi_app.state.mongo_db
                # 使用同步方式获取任务
                task = db.social_tasks.find_one({"task_id": task_id})

                if task:
                    # 获取最近的日志
                    logs = []
                    try:
                        # 使用异步方式获取日志
                        log_cursor = db.social_task_logs.find({"task_id": task_id}).sort("created_at", -1).limit(10)
                        logs = await log_cursor.to_list(length=10)
                        # 反转日志顺序，使其按时间升序排列
                        logs.reverse()
                    except Exception as log_error:
                        logger.error(f"获取任务 {task_id} 日志失败: {str(log_error)}", exc_info=True)

                    # 构建基本状态信息
                    status_data = {
                        "task_id": task_id,
                        "status": task.get("status", "pending"),
                        "progress": task.get("progress", 0),
                        "start_time": task.get("start_time", ""),
                        "estimated_end_time": task.get("estimated_end_time", ""),
                        "device_usage": task.get("device_usage", {
                            "cpu": 0,
                            "memory": 0,
                            "network": "未知"
                        }),
                        "logs": [
                            {
                                "message": log.get("message", ""),
                                "level": log.get("level", "info"),
                                "timestamp": log.get("timestamp", "")
                            }
                            for log in logs
                        ]
                    }

                    # 发送状态更新
                    await sio.emit("task_status_update", status_data, room=sid)
                    logger.info(f"已从MongoDB向客户端 {sid} 发送任务 {task_id} 的初始状态")

                    # 同时保存到Redis
                    if hasattr(fastapi_app.state, 'redis'):
                        try:
                            redis_client = fastapi_app.state.redis
                            key = f"task:{task_id}:latest"
                            # 确保status_data是可序列化的
                            status_json = json.dumps(status_data)
                            await redis_client.set(key, status_json)
                            await redis_client.expire(key, 86400)  # 24小时过期
                            logger.debug(f"已将任务 {task_id} 状态保存到Redis")

                            # 同时发布到Redis通道，以便其他服务可以接收
                            channel = f"task:{task_id}:status"
                            await redis_client.publish(channel, status_json)
                            logger.debug(f"已将任务 {task_id} 状态发布到Redis通道 {channel}")
                        except Exception as redis_error:
                            logger.error(f"保存任务 {task_id} 状态到Redis失败: {str(redis_error)}", exc_info=True)
                else:
                    logger.warning(f"任务 {task_id} 不存在，无法发送初始状态")

                    # 发送空状态，避免前端一直等待
                    empty_status = {
                        "task_id": task_id,
                        "status": "unknown",
                        "progress": 0,
                        "logs": [
                            {
                                "message": "任务不存在或已被删除",
                                "level": "warning",
                                "timestamp": datetime.datetime.now().isoformat()
                            }
                        ]
                    }
                    await sio.emit("task_status_update", empty_status, room=sid)
        except Exception as e:
            logger.error(f"获取并发送任务 {task_id} 初始状态失败: {str(e)}", exc_info=True)

            # 发送错误状态，避免前端一直等待
            error_status = {
                "task_id": task_id,
                "status": "error",
                "progress": 0,
                "logs": [
                    {
                        "message": f"获取任务状态失败: {str(e)}",
                        "level": "error",
                        "timestamp": datetime.datetime.now().isoformat()
                    }
                ]
            }
            await sio.emit("task_status_update", error_status, room=sid)

    # 取消任务状态订阅
    @sio.on('unsubscribe_task')
    async def handle_unsubscribe_task(sid, task_id):
        """取消订阅任务状态更新"""
        logger.info(f"客户端 {sid} 取消订阅任务 {task_id} 状态")
        # 将客户端从任务特定的房间移除
        await sio.leave_room(sid, f"task_{task_id}")

    # 注册socket.io事件处理器
    @sio.on("arrange_layout")
    async def handle_arrange_layout(sid, data, callback=None):
        """处理布局请求"""
        logger.info(f"Received arrange_layout request from {sid}")

    @router.get("/", response_model=List[dict])
    async def get_devices(
        skip: int = 0,
        limit: int = 100,
        include_config: bool = False,
        include_mappings: bool = True,  # 默认包含关联信息
        core_id: str = None,  # 添加Core服务ID参数
        repo: DeviceRepository = Depends(get_device_repo)
    ):
        logger = logging.getLogger(__name__)
        logger.info("获取设备列表请求，参数: skip=%d, limit=%d, include_config=%s, include_mappings=%s, core_id=%s",
                   skip, limit, include_config, include_mappings, core_id)

        try:
            # 构建查询条件
            query = {}
            if core_id:
                query["core_id"] = core_id

            devices = await repo.get_devices(skip=skip, limit=limit, query=query)
            logger.debug(f"获取到 {len(devices)} 个设备")

            # 检查设备数据结构
            if devices and not isinstance(devices, list):
                logger.error(f"设备数据类型错误: {type(devices)}")
                devices = []

            # 如果需要包含关联信息，获取所有设备的关联信息
            device_mappings = {}
            if include_mappings:
                try:
                    # 获取所有设备ID和MongoDB _id
                    device_ids = []
                    device_id_mapping = {}  # 用于记录设备ID的映射关系

                    for device in devices:
                        if not device:
                            continue

                        # 优先使用MongoDB _id作为主要设备ID
                        primary_device_id = str(device.get('_id', device.get('id', 'unknown')))

                        # 添加所有可能的设备ID变体
                        possible_ids = []

                        # MongoDB _id
                        if '_id' in device:
                            mongo_id = str(device['_id'])
                            possible_ids.append(mongo_id)

                        # 传统id字段
                        if 'id' in device:
                            id_field = str(device['id'])
                            possible_ids.append(id_field)

                        # device_id字段（如果存在）
                        if 'device_id' in device:
                            device_id_field = str(device['device_id'])
                            possible_ids.append(device_id_field)

                        # 去重并添加到查询列表
                        for possible_id in set(possible_ids):
                            if possible_id != 'unknown':
                                device_ids.append(possible_id)
                                device_id_mapping[possible_id] = primary_device_id

                    # 从MongoDB中获取设备账号映射
                    if device_ids and hasattr(fastapi_app.state, 'mongo_db'):
                        db = fastapi_app.state.mongo_db
                        # 添加调试日志
                        logger.info(f"查询设备账号映射，设备ID: {device_ids}")

                        # 获取所有设备账号映射，然后在内存中过滤
                        # 这是一个临时解决方案，因为设备ID格式可能不一致
                        all_mappings_cursor = db.device_account_mappings.find({})
                        all_mappings = await all_mappings_cursor.to_list(length=1000)

                        # 在内存中过滤映射
                        mappings = []
                        for mapping in all_mappings:
                            mapping_device_id = str(mapping.get('device_id', ''))
                            if mapping_device_id in device_ids:
                                mappings.append(mapping)

                        logger.info(f"查询到 {len(mappings)} 个设备账号映射，总共 {len(all_mappings)} 个映射")

                        # 不需要再次查询，因为我们已经在内存中过滤了映射

                        # 添加调试日志
                        logger.info(f"查询到 {len(mappings)} 个设备账号映射")

                        # 按设备ID组织映射
                        for mapping in mappings:
                            mapping_device_id = str(mapping.get('device_id', ''))
                            if mapping_device_id:
                                # 找到对应的主要设备ID
                                primary_device_id = device_id_mapping.get(mapping_device_id, mapping_device_id)

                                if primary_device_id not in device_mappings:
                                    device_mappings[primary_device_id] = []

                                # 格式化映射数据
                                formatted_mapping = {
                                    'id': str(mapping.get('_id', '')),
                                    'account_id': str(mapping.get('account_id', '')),
                                    'platform_id': str(mapping.get('platform_id', '')),
                                    'app_id': str(mapping.get('app_id', '')),
                                    'created_at': mapping.get('created_at', ''),
                                    'updated_at': mapping.get('updated_at', '')
                                }

                                # 添加设置信息
                                if 'settings' in mapping:
                                    formatted_mapping['settings'] = mapping.get('settings', {})

                                device_mappings[primary_device_id].append(formatted_mapping)

                        logger.info(f"获取到 {len(mappings)} 个设备账号映射，涉及 {len(device_mappings)} 个设备")
                except Exception as mapping_error:
                    logger.error(f"获取设备账号映射失败: {str(mapping_error)}", exc_info=True)

            result = []
            for device in devices:
                try:
                    # 检查设备数据是否有效
                    if not device or not isinstance(device, dict):
                        logger.warning(f"跳过无效设备数据: {device}")
                        continue

                    # 安全地获取设备属性
                    device_id = str(device.get('_id', device.get('id', 'unknown')))
                    device_data = {
                        'id': device_id,
                        'name': device.get('name', f'设备-{device_id}'),
                        'type': device.get('type', 'unknown'),  # 使用MongoDB中的type字段
                        'status': device.get('status', 'unknown'),
                        'updated_at': device.get('updated_at', datetime.datetime.now().isoformat()),
                        'cpu': device.get('cpu_usage', '0%'),
                        'memory': device.get('memory_usage', '0%'),
                        'network': device.get('network_status', 'unknown'),
                        'core_id': device.get('core_id', 'default')  # 添加Core服务ID
                    }

                    # 添加显示信息
                    if 'display_info' in device:
                        device_data['display_info'] = device.get('display_info', {})
                    else:
                        # 提供默认的显示信息
                        device_data['display_info'] = {
                            'width': 1080,
                            'height': 1920,
                            'dpi': 320
                        }

                    # 根据请求参数决定是否包含配置
                    if include_config and 'config' in device:
                        device_data['config'] = device.get('config', {})

                    # 添加设备关联信息
                    if include_mappings:
                        device_data['mappings'] = device_mappings.get(device_id, [])

                    # 添加代理IP关联信息
                    proxy_info = await get_device_proxy_info(device_id)
                    if proxy_info:
                        device_data['proxy_info'] = proxy_info

                    result.append(device_data)
                except Exception as item_error:
                    logger.error(f"处理设备项目时出错: {str(item_error)}", exc_info=True)
                    # 继续处理下一个设备

            logger.info(f"成功返回 {len(result)} 个设备")
            return result

        except Exception as e:
            logger.error("获取设备列表异常: %s", str(e), exc_info=True)
            # 返回空列表而不是抛出异常，避免前端崩溃
            return []

    @router.post("/", response_model=dict)
    async def create_device(
        request: Request,
        repo: DeviceRepository = Depends(get_device_repo)
    ):
        device_data = await request.json()
        device_data["created_at"] = datetime.datetime.now()
        device_data["status"] = "stopped"

        device_id = repo.create_device(device_data)
        return await repo.get_device(device_id)

    @router.put("/{device_id}", response_model=dict)
    async def update_device(
        device_id: str,
        request: Request,
        repo: DeviceRepository = Depends(get_device_repo)
    ):
        update_data = await request.json()

        if len(update_data) == 0:
            raise HTTPException(status_code=400, detail="无更新数据")

        if not await repo.update_device(device_id, update_data):
            raise HTTPException(status_code=404, detail="设备未找到或未更新")

        return await repo.get_device(device_id)

    # WebSocket事件处理器
    @sio.on("get_devices")
    async def handle_get_devices(sid, callback=None):
        """获取设备列表"""
        try:
            repo = DeviceRepository(sio.app.state.mongo_db)
            devices = await repo.get_devices(limit=100)

            # 转换设备数据为前端期望的格式
            formatted_devices = []
            for device in devices:
                if not device or not isinstance(device, dict):
                    continue

                device_id = str(device.get('_id', device.get('id', 'unknown')))
                device_data = {
                    'id': device_id,
                    'name': device.get('name', f'设备-{device_id}'),
                    'type': device.get('type', 'unknown'),  # 使用MongoDB中的type字段
                    'status': device.get('status', 'unknown'),
                    'cpu': device.get('cpu_usage', '0%'),
                    'memory': device.get('memory_usage', '0%'),
                    'network': device.get('network_status', 'unknown')
                }

                # 添加显示信息
                if 'display_info' in device:
                    device_data['display_info'] = device.get('display_info', {})
                else:
                    # 提供默认的显示信息
                    device_data['display_info'] = {
                        'width': 1080,
                        'height': 1920,
                        'dpi': 320
                    }

                # 添加配置信息
                if 'config' in device:
                    device_data['config'] = device.get('config', {})

                formatted_devices.append(device_data)

            if callback:
                logger.debug(f"通过WebSocket返回 {len(formatted_devices)} 个设备")
                await callback(formatted_devices)

            return formatted_devices
        except Exception as e:
            logger.error(f"WebSocket获取设备列表失败: {str(e)}", exc_info=True)
            if callback:
                await callback([])
            return []

    @sio.on("start_device")
    async def handle_start_device(sid, device_id, callback=None):
        """启动单个设备(新架构)"""
        try:
            # 使用应用状态中的设备服务
            try:
                service = fastapi_app.state.device_service
                if not service:
                    logger.warning(f"应用状态中没有设备服务，无法启动设备 {device_id}")
                    if callback:
                        await callback({"success": False, "error": "设备服务不可用"})
                    return

                # 调用设备服务启动设备
                success = await service.start_device(device_id)
                if success:
                    device = await service.get_device_status(device_id)
                    await sio.emit("device_status_update", device, to=sid)
                if callback:
                    await callback({"success": success})
            except AttributeError:
                logger.error(f"设备服务缺少必要的方法，无法启动设备 {device_id}")
                if callback:
                    await callback({"success": False, "error": "设备服务配置错误"})
        except Exception as e:
            logger.error(f"启动设备失败: {device_id} - {str(e)}", exc_info=True)
            if callback:
                await callback({"success": False, "error": str(e)})

    @sio.on("batch_start_devices")
    async def handle_batch_start(sid, device_ids, callback):
        """批量启动设备(新架构)"""
        try:
            # 使用应用状态中的设备服务
            try:
                service = fastapi_app.state.device_service
                if not service:
                    logger.warning(f"应用状态中没有设备服务，无法批量启动设备")
                    await callback({
                        "success": False,
                        "error": "设备服务不可用",
                        "results": [{
                            "success": False,
                            "message": "设备服务不可用",
                            "deviceId": did
                        } for did in device_ids]
                    })
                    return

                # 调用设备服务批量启动设备
                results = await service.batch_operation('start', device_ids)

                # 通知状态变更
                for device_id in device_ids:
                    if results.get(device_id, False):
                        try:
                            device = await service.get_device_status(device_id)
                            await sio.emit(
                                "device_status_update",
                                jsonable_encoder(device),
                                to=sid
                            )
                        except Exception as status_error:
                            logger.error(f"获取设备状态失败: {device_id} - {str(status_error)}")

                await callback({
                    "success": True,
                    "results": [{
                        "success": results.get(did, False),
                        "message": "启动成功" if results.get(did, False) else "启动失败",
                        "deviceId": did
                    } for did in device_ids]
                })
            except AttributeError as attr_error:
                logger.error(f"设备服务缺少必要的方法: {str(attr_error)}")
                await callback({
                    "success": False,
                    "error": "设备服务配置错误",
                    "details": str(attr_error)
                })
        except Exception as e:
            logger.error(f"批量启动失败: {str(e)}", exc_info=True)
            await callback({
                "success": False,
                "error": str(e)
            })

    @sio.on("stop_device")
    async def handle_stop_device(sid, device_id, callback=None):
        """停止单个设备(新架构)"""
        try:
            # 使用应用状态中的设备服务
            try:
                service = fastapi_app.state.device_service
                if not service:
                    logger.warning(f"应用状态中没有设备服务，无法停止设备 {device_id}")
                    if callback:
                        await callback({"success": False, "error": "设备服务不可用"})
                    return

                # 调用设备服务停止设备
                success = await service.stop_device(device_id)
                if success:
                    device = await service.get_device_status(device_id)
                    await sio.emit("device_status_update", device, to=sid)
                if callback:
                    await callback({"success": success})
            except AttributeError:
                logger.error(f"设备服务缺少必要的方法，无法停止设备 {device_id}")
                if callback:
                    await callback({"success": False, "error": "设备服务配置错误"})
        except Exception as e:
            logger.error(f"停止设备失败: {device_id} - {str(e)}", exc_info=True)
            if callback:
                await callback({"success": False, "error": str(e)})

    @sio.on("batch_stop_devices")
    async def handle_batch_stop(sid, device_ids, callback):
        """批量停止设备(新架构)"""
        try:
            # 使用应用状态中的设备服务
            try:
                service = fastapi_app.state.device_service
                if not service:
                    logger.warning(f"应用状态中没有设备服务，无法批量停止设备")
                    await callback({
                        "success": False,
                        "error": "设备服务不可用",
                        "results": [{
                            "success": False,
                            "message": "设备服务不可用",
                            "deviceId": did
                        } for did in device_ids]
                    })
                    return

                # 调用设备服务批量停止设备
                results = await service.batch_operation('stop', device_ids)

                # 通知状态变更
                for device_id in device_ids:
                    if results.get(device_id, False):
                        try:
                            device = await service.get_device_status(device_id)
                            await sio.emit(
                                "device_status_update",
                                jsonable_encoder(device),
                                to=sid
                            )
                        except Exception as status_error:
                            logger.error(f"获取设备状态失败: {device_id} - {str(status_error)}")

                await callback({
                    "success": True,
                    "results": [{
                        "success": results.get(did, False),
                        "message": "停止成功" if results.get(did, False) else "停止失败",
                        "deviceId": did
                    } for did in device_ids]
                })
            except AttributeError as attr_error:
                logger.error(f"设备服务缺少必要的方法: {str(attr_error)}")
                await callback({
                    "success": False,
                    "error": "设备服务配置错误",
                    "details": str(attr_error)
                })
        except Exception as e:
            logger.error(f"批量停止失败: {str(e)}", exc_info=True)
            await callback({
                "success": False,
                "error": str(e)
            })

    # 自动布局相关WebSocket接口
    @sio.on("calculate_layout")
    async def handle_calculate_layout(sid, config, callback=None):
        """计算设备自动布局"""
        repo = DeviceRepository(sio.app.state.mongo_db)
        # 获取设备数据
        devices = await repo.get_devices(limit=100)

        # 示例布局算法 - 网格布局
        cols = int(len(devices)**0.5) + 1
        for i, device in enumerate(devices):
            layout = {
                'x': (i % cols) * 200,
                'y': (i // cols) * 150,
                'width': 180,
                'height': 120
            }
            # 更新数据库
            await repo.update_device(device["_id"], {"layout": layout})
            device['layout'] = layout

        if callback:
            await callback({
                "success": True,
                "devices": jsonable_encoder(devices)
            })

        # 广播布局更新
        for device in devices:
            await sio.emit(
                "device_layout_update",
                jsonable_encoder({
                    "deviceId": device["_id"],
                    "layout": device["layout"]
                }),
                to=sid
            )

    # 自动布局接口
    @sio.on("arrange_layout")
    async def handle_arrange_layout(sid, layout_type='grid', callback=None):
        """雷电模拟器窗口自动排序处理"""
        logger.info(f"开始{layout_type}布局处理")

        try:
            # 获取设备服务
            try:
                # 尝试从应用状态获取设备服务
                service = fastapi_app.state.device_service
                if not service:
                    logger.warning("应用状态中没有设备服务，使用模拟服务")
                    service = {
                        "sort_windows": lambda: False,
                        "get_running_list": lambda: []
                    }
                logger.debug(f"服务实例: {type(service).__name__ if hasattr(service, '__class__') else type(service)}")
            except Exception as e:
                logger.error(f"获取设备服务失败: {str(e)}", exc_info=True)
                service = {
                    "sort_windows": lambda: False,
                    "get_running_list": lambda: []
                }

            # 执行窗口排序
            sort_success = await service.sort_windows()
            logger.info(f"窗口排序结果: {sort_success}")

            # 获取运行设备列表
            running_devices = await service.get_running_list()
            logger.debug(f"运行设备列表: {running_devices}")

            # 构建返回结果
            result = {
                "status": "success",
                "layout": layout_type,
                "sorted": sort_success,
                "device_count": len(running_devices),
                "devices": running_devices,
                "details": {
                    "message": f"窗口排序成功，共{len(running_devices)}台设备" if sort_success else "窗口排序失败"
                }
            }
            logger.debug(f"返回结果: {result}")

            # 回调通知
            if callback:
                await callback(result)

            # 广播事件
            await sio.emit(
                "layout_updated",
                result,
                to=sid
            )

            return result

        except Exception as e:
            error_msg = f"布局排序异常: {str(e)}"
            logger.error(error_msg, exc_info=True)
            if callback:
                await callback(jsonable_encoder({
                    "status": "error",
                    "message": error_msg
                }))
            return

    # ------ WebSocket会话清理 ------
    async def _simple_cleanup():
        """最小化实现的会话清理"""
        try:
            while True:
                await asyncio.sleep(300)
                for sid in list(sio.manager.rooms.keys()):
                    if sid not in sio.manager.connected:
                        await sio.disconnect(sid)
                        logger.debug(f"Cleanup disconnected: {sid}")
        except Exception as e:
            logger.error(f"Cleanup error: {e}")

    # 启动WebSocket会话清理任务 - 延迟到应用启动时执行
    # asyncio.create_task(_simple_cleanup())  # 注释掉，避免在模块导入时创建任务

    # 添加启动事件处理器来启动清理任务
    @fastapi_app.on_event("startup")
    async def startup_cleanup_task():
        """应用启动时启动清理任务"""
        try:
            asyncio.create_task(_simple_cleanup())
            logger.info("WebSocket会话清理任务已启动")
        except Exception as e:
            logger.error(f"启动清理任务失败: {e}")

    # 设备同步API端点 - 处理Core推送的设备状态变更
    @sync_router.post("/sync")
    async def sync_device_status(request: Request):
        """
        处理Core推送的设备状态变更

        此API端点接收来自Core的设备状态变更消息，并通过WebSocket推送给前端
        """
        logger = logging.getLogger(__name__)
        logger.info("收到设备状态同步请求")

        try:
            # 解析请求数据
            data = await request.json()
            changes = data.get("changes", [])
            timestamp = data.get("timestamp", 0)

            if not changes:
                logger.warning("同步请求中没有变更数据")
                return {"status": "success", "message": "没有变更数据需要处理"}

            logger.info(f"收到{len(changes)}个设备状态变更")

            # 更新设备状态到数据库
            repo = DeviceRepository(request.app.state.mongo_db)
            updated_devices = []

            for change in changes:
                device_id = change.get("device_id")
                if not device_id:
                    continue

                # 更新设备状态
                update_data = {
                    "status": change.get("new_status"),
                    "updated_at": datetime.datetime.fromtimestamp(timestamp)
                }

                success = await repo.update_device(device_id, update_data)
                if success:
                    # 获取更新后的设备信息
                    device = await repo.get_device(device_id)
                    if device:
                        updated_devices.append(device)

                        # 通过WebSocket推送状态更新
                        await sio.emit(
                            "device_status_update",
                            jsonable_encoder({
                                "id": str(device.id),
                                "name": device.name,
                                "type": device.get('device_type', 'unknown'),
                                "status": device.status,
                                "updated_at": device.updated_at.isoformat(),
                                "cpu": device.get('cpu_usage', '0%'),
                                "memory": device.get('memory_usage', '0%'),
                                "network": device.get('network_status', 'unknown'),
                                "display_info": device.get('display_info', {
                                    "width": 1080,
                                    "height": 1920,
                                    "dpi": 320
                                })
                            })
                        )

            return {
                "status": "success",
                "message": f"成功处理{len(updated_devices)}个设备状态变更",
                "updated_devices": len(updated_devices)
            }

        except Exception as e:
            logger.error(f"处理设备状态同步异常: {str(e)}", exc_info=True)
            return {
                "status": "error",
                "message": f"处理设备状态同步失败: {str(e)}"
            }

    # 添加重置设备状态的API端点
    @sync_router.post("/reset-status/{device_id}")
    async def reset_device_status(
        device_id: str,
        status: str = "stopped",
        repo: DeviceRepository = Depends(get_device_repo)
    ):
        """重置设备状态

        用于手动修复设备状态不一致的问题
        """
        logger = logging.getLogger(__name__)
        logger.info(f"手动重置设备{device_id}状态为{status}")

        update_data = {
            "status": status,
            "updated_at": datetime.datetime.now()
        }

        success = await repo.update_device(device_id, update_data)
        if not success:
            raise HTTPException(status_code=404, detail="设备未找到")

        # 获取更新后的设备信息
        device = await repo.get_device(device_id)

        # 通知前端
        if device:
            await sio.emit(
                "device_status_update",
                jsonable_encoder({
                    "id": str(device.id),
                    "name": device.name,
                    "status": device.status,
                    "updated_at": device.updated_at.isoformat()
                })
            )

        return {
            "success": True,
            "message": f"设备{device_id}状态已重置为{status}",
            "device": jsonable_encoder(device) if device else None
        }

    # 添加批量重置设备状态的API端点
    @sync_router.post("/reset-all-statuses")
    async def reset_all_device_statuses(
        status: str = "stopped",
        repo: DeviceRepository = Depends(get_device_repo)
    ):
        """重置所有设备状态

        用于批量修复设备状态不一致的问题
        """
        logger = logging.getLogger(__name__)
        logger.info(f"批量重置所有设备状态为{status}")

        # 获取所有设备
        devices = await repo.get_devices()
        updated_count = 0
        updated_devices = []

        for device in devices:
            if device.status == "starting":  # 只重置处于starting状态的设备
                # 更新设备状态
                update_data = {
                    "status": status,
                    "updated_at": datetime.datetime.now()
                }

                success = await repo.update_device(device.id, update_data)
                if success:
                    updated_count += 1

                    # 获取更新后的设备信息
                    updated_device = await repo.get_device(device.id)
                    if updated_device:
                        updated_devices.append(updated_device)

                        # 通知前端
                        await sio.emit(
                            "device_status_update",
                            jsonable_encoder({
                                "id": str(updated_device.id),
                                "name": updated_device.name,
                                "status": updated_device.status,
                                "updated_at": updated_device.updated_at.isoformat()
                            })
                        )

        return {
            "success": True,
            "message": f"已重置{updated_count}个设备状态为{status}",
            "updated_count": updated_count,
            "devices": jsonable_encoder(updated_devices)
        }

    # 将sync_router添加到app中
    fastapi_app.include_router(sync_router)

    return router