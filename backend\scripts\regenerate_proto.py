#!/usr/bin/env python3
"""
重新生成 protobuf 代码的脚本
使用兼容的 protobuf 版本生成代码
"""

import os
import subprocess
import sys
from pathlib import Path

def run_command(cmd, cwd=None):
    """运行命令并返回结果"""
    print(f"运行命令: {' '.join(cmd)}")
    result = subprocess.run(cmd, cwd=cwd, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"命令失败: {result.stderr}")
        return False
    print(f"命令成功: {result.stdout}")
    return True

def main():
    # 获取当前脚本目录
    script_dir = Path(__file__).parent
    proto_dir = script_dir / "app" / "proto"
    core_proto_dir = script_dir.parent / "core" / "src" / "api"
    
    print(f"Proto目录: {proto_dir}")
    print(f"Core Proto目录: {core_proto_dir}")
    
    # 确保目录存在
    proto_dir.mkdir(parents=True, exist_ok=True)
    
    # 要复制的proto文件
    proto_files = ["device.proto", "file.proto", "task.proto"]
    
    # 复制proto文件
    for proto_file in proto_files:
        src = core_proto_dir / proto_file
        dst = proto_dir / proto_file
        
        if src.exists():
            print(f"复制 {src} 到 {dst}")
            with open(src, 'r', encoding='utf-8') as f:
                content = f.read()
            with open(dst, 'w', encoding='utf-8') as f:
                f.write(content)
        else:
            print(f"警告: 源文件不存在 {src}")
    
    # 生成Python代码
    for proto_file in proto_files:
        proto_path = proto_dir / proto_file
        if proto_path.exists():
            print(f"生成 {proto_file} 的Python代码...")
            
            # 使用 --experimental_allow_proto3_optional 标志来避免新特性
            cmd = [
                sys.executable, "-m", "grpc_tools.protoc",
                f"-I{proto_dir}",
                f"--python_out={proto_dir}",
                f"--grpc_python_out={proto_dir}",
                "--experimental_allow_proto3_optional",
                str(proto_path)
            ]
            
            if not run_command(cmd):
                print(f"生成 {proto_file} 失败")
                return False
    
    print("所有 protobuf 代码生成完成!")
    return True

if __name__ == "__main__":
    if main():
        print("成功!")
        sys.exit(0)
    else:
        print("失败!")
        sys.exit(1)
