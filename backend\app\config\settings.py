"""
应用设置
"""

import os
from pydantic_settings import BaseSettings, SettingsConfigDict
from functools import lru_cache

class Settings(BaseSettings):
    """应用设置类"""

    # 应用设置
    app_name: str = "ThunderHub Backend"
    debug: bool = True

    # 数据库设置
    mongodb_url: str = "mongodb://localhost:27017"
    mongodb_name: str = "thunderhub"
    redis_url: str = "redis://localhost:6379/1"
    consul_url: str = "http://localhost:8500"

    # 数据库连接池设置
    pool_min: int = 2
    pool_max: int = 20
    echo: bool = False

    # Consul设置（从consul_url解析）
    @property
    def consul_host(self) -> str:
        from urllib.parse import urlparse
        parsed = urlparse(self.consul_url)
        return parsed.hostname or "localhost"

    @property
    def consul_port(self) -> int:
        from urllib.parse import urlparse
        parsed = urlparse(self.consul_url)
        return parsed.port or 8500

    # Core服务设置
    core_default_host: str = "***************"
    core_default_port: int = 50051

    # JWT设置
    secret_key: str = "your-secret-key"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 120  # JWT过期时间从30分钟增加到120分钟

    model_config = SettingsConfigDict(
        env_file=".env",  # 相对于工作目录的路径
        env_file_encoding="utf-8",
        env_prefix="APP_",
        extra="ignore"
    )

def get_settings():
    """获取应用设置（不使用缓存以便调试）"""
    return Settings()

