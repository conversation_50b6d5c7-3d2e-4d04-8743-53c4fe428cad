# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from . import task_pb2 as task__pb2


class TaskServiceStub(object):
    """任务服务
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateTask = channel.unary_unary(
                '/task.TaskService/CreateTask',
                request_serializer=task__pb2.TaskRequest.SerializeToString,
                response_deserializer=task__pb2.TaskResponse.FromString,
                )
        self.StartTask = channel.unary_unary(
                '/task.TaskService/StartTask',
                request_serializer=lambda x: b"",
                response_deserializer=lambda x: task__pb2.TaskResponse(),
                )
        self.PauseTask = channel.unary_unary(
                '/task.TaskService/PauseTask',
                request_serializer=lambda x: b"",
                response_deserializer=lambda x: task__pb2.TaskResponse(),
                )
        self.CancelTask = channel.unary_unary(
                '/task.TaskService/CancelTask',
                request_serializer=lambda x: b"",
                response_deserializer=lambda x: task__pb2.TaskResponse(),
                )
        self.GetTaskStatus = channel.unary_unary(
                '/task.TaskService/GetTaskStatus',
                request_serializer=lambda x: b"",
                response_deserializer=lambda x: task__pb2.TaskStatusResponse(),
                )
        self.GetTaskLogs = channel.unary_unary(
                '/task.TaskService/GetTaskLogs',
                request_serializer=lambda x: b"",
                response_deserializer=lambda x: task__pb2.TaskLogsResponse(),
                )
        self.GetWorkflowConfig = channel.unary_unary(
                '/task.TaskService/GetWorkflowConfig',
                request_serializer=lambda x: b"",
                response_deserializer=lambda x: task__pb2.WorkflowConfigResponse(),
                )


class TaskServiceServicer(object):
    """任务服务
    """

    def CreateTask(self, request, context):
        """创建任务
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartTask(self, request, context):
        """开始执行任务
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PauseTask(self, request, context):
        """暂停任务
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CancelTask(self, request, context):
        """取消任务
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTaskStatus(self, request, context):
        """获取任务状态
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTaskLogs(self, request, context):
        """获取任务日志
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetWorkflowConfig(self, request, context):
        """获取工作流配置
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_TaskServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateTask,
                    request_deserializer=lambda x: task__pb2.TaskRequest(),
                    response_serializer=lambda x: b"",
            ),
            'StartTask': grpc.unary_unary_rpc_method_handler(
                    servicer.StartTask,
                    request_deserializer=lambda x: task__pb2.TaskIdRequest(),
                    response_serializer=lambda x: b"",
            ),
            'PauseTask': grpc.unary_unary_rpc_method_handler(
                    servicer.PauseTask,
                    request_deserializer=lambda x: task__pb2.TaskIdRequest(),
                    response_serializer=lambda x: b"",
            ),
            'CancelTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CancelTask,
                    request_deserializer=lambda x: task__pb2.TaskIdRequest(),
                    response_serializer=lambda x: b"",
            ),
            'GetTaskStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTaskStatus,
                    request_deserializer=lambda x: task__pb2.TaskIdRequest(),
                    response_serializer=lambda x: b"",
            ),
            'GetTaskLogs': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTaskLogs,
                    request_deserializer=lambda x: task__pb2.TaskIdRequest(),
                    response_serializer=lambda x: b"",
            ),
            'GetWorkflowConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.GetWorkflowConfig,
                    request_deserializer=lambda x: task__pb2.WorkflowConfigRequest(),
                    response_serializer=lambda x: b"",
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'task.TaskService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class TaskService(object):
    """任务服务
    """

    @staticmethod
    def CreateTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/task.TaskService/CreateTask',
            lambda x: b"",
            lambda x: task__pb2.TaskResponse(),
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StartTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/task.TaskService/StartTask',
            lambda x: b"",
            lambda x: task__pb2.TaskResponse(),
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetWorkflowConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/task.TaskService/GetWorkflowConfig',
            lambda x: b"",
            lambda x: task__pb2.WorkflowConfigResponse(),
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
