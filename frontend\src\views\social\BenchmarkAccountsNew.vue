<template>
  <div class="benchmark-accounts-new">
    <!-- 页面头部 -->
    <div class="management-header">
      <h1>🎯 对标账号管理</h1>
      <p class="header-description">以关联关系为核心，清晰展示我们的账号对标哪些频道，一目了然</p>
      <div class="status-notice">
        <el-alert
          title="显示所有账号，统计数据仅计算活跃状态账号，非活跃账号以半透明显示"
          type="info"
          :closable="false"
          show-icon
        />
      </div>
    </div>

    <!-- 调试信息 (开发模式) -->
    <div v-if="showDebugInfo" class="debug-info">
      <el-card>
        <template #header>
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span>🔍 调试信息</span>
            <el-button size="small" @click="showDebugInfo = false">隐藏</el-button>
          </div>
        </template>
        <div class="debug-content">
          <div class="debug-section">
            <h4>我们的账号 ({{ ourAccounts.length }}个，活跃: {{ ourAccounts.filter(a => a.status === 'active').length }}个)</h4>
            <div class="debug-list">
              <div v-for="account in ourAccounts" :key="account.id || account._id" class="debug-item" :class="{ 'inactive': account.status !== 'active' }">
                <span class="status-badge" :class="account.status">{{ account.status || 'unknown' }}</span>
                ID: {{ account.id || account._id }} | 名称: {{ account.name || account.display_name }} | 平台: {{ account.platform }}
              </div>
            </div>
          </div>
          <div class="debug-section">
            <h4>对标账号 ({{ benchmarkAccounts.length }}个，活跃: {{ benchmarkAccounts.filter(b => b.status === 'active' || !b.status).length }}个)</h4>
            <div class="debug-list">
              <div v-for="benchmark in benchmarkAccounts" :key="benchmark._id || benchmark.id" class="debug-item" :class="{ 'inactive': benchmark.status && benchmark.status !== 'active' }">
                <span class="status-badge" :class="benchmark.status || 'active'">{{ benchmark.status || 'active' }}</span>
                {{ benchmark.account_name }} -> 关联ID: {{ benchmark.our_account_id }}
              </div>
            </div>
          </div>
          <div class="debug-section">
            <h4>关联统计 (仅统计活跃账号)</h4>
            <p>有活跃对标账号的活跃我们的账号: {{ stats.activeRelations }}/{{ stats.ourAccountsCount }}</p>
            <p>平均活跃对标数: {{ stats.avgBenchmarksPerAccount }}</p>
            <p>总账号数: {{ ourAccounts.length }} (活跃: {{ ourAccounts.filter(a => a.status === 'active').length }})</p>
            <p>总对标数: {{ benchmarkAccounts.length }} (活跃: {{ benchmarkAccounts.filter(b => b.status === 'active' || !b.status).length }})</p>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">👥</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.ourAccountsCount }}</div>
                <div class="stat-label">活跃账号</div>
                <div class="stat-detail">我们的活跃账号数量</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">📊</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.totalBenchmarks }}</div>
                <div class="stat-label">活跃对标</div>
                <div class="stat-detail">活跃状态的对标账号</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">🔗</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.activeRelations }}</div>
                <div class="stat-label">活跃关联</div>
                <div class="stat-detail">{{ stats.activeRelations }}/{{ stats.ourAccountsCount }} 个活跃账号有对标</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">⚡</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.avgBenchmarksPerAccount }}</div>
                <div class="stat-label">平均对标数</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <!-- 视图切换 -->
        <el-radio-group v-model="viewMode" @change="handleViewModeChange" class="view-mode-switch">
          <el-radio-button value="grouped">📋 分组视图</el-radio-button>
          <el-radio-button value="list">📄 列表视图</el-radio-button>
        </el-radio-group>

        <!-- 搜索框 -->
        <el-input
          v-model="searchQuery"
          placeholder="搜索账号名称..."
          style="width: 250px; margin-left: 15px"
          @input="handleSearch"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <!-- 筛选器 -->
        <el-select
          v-model="filterPlatform"
          placeholder="平台筛选"
          style="width: 120px; margin-left: 10px"
          @change="handleFilter"
          clearable
        >
          <el-option label="YouTube" value="youtube" />
          <el-option label="TikTok" value="tiktok" />
          <el-option label="Instagram" value="instagram" />
          <el-option label="抖音" value="douyin" />
          <el-option label="微博" value="weibo" />
          <el-option label="小红书" value="xiaohongshu" />
          <el-option label="快手" value="kuaishou" />
          <el-option label="B站" value="bilibili" />
        </el-select>

        <el-select
          v-model="filterType"
          placeholder="类型筛选"
          style="width: 120px; margin-left: 10px"
          @change="handleFilter"
          clearable
        >
          <el-option label="原创" value="original" />
          <el-option label="二创" value="recreate" />
          <el-option label="搬运" value="repost" />
        </el-select>
      </div>

      <div class="toolbar-right">
        <el-button @click="showDebugInfo = !showDebugInfo" :type="showDebugInfo ? 'warning' : ''">
          <el-icon><Search /></el-icon>
          {{ showDebugInfo ? '隐藏调试' : '调试信息' }}
        </el-button>
        <el-button @click="loadData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="primary" @click="showAddDialog">
          <el-icon><Plus /></el-icon>
          添加对标账号
        </el-button>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content" v-loading="loading">
      <!-- 分组视图 -->
      <div v-if="viewMode === 'grouped'" class="grouped-view">
        <div
          v-for="group in groupedData"
          :key="group.ourAccount.id"
          class="account-group"
          :class="{ 'inactive-account': group.ourAccount.status !== 'active' }"
        >
          <!-- 我们的账号卡片 -->
          <div class="our-account-card">
            <div class="account-header">
              <div class="account-avatar">
                <img v-if="group.ourAccount.avatar" :src="group.ourAccount.avatar" :alt="group.ourAccount.name" />
                <div v-else class="avatar-placeholder">
                  {{ getAccountInitial(group.ourAccount.name) }}
                </div>
              </div>
              <div class="account-info">
                <h3 class="account-name">{{ formatAccountName(group.ourAccount) }}</h3>
                <div class="account-meta">
                  <el-tag size="small" :type="getPlatformTagType(group.ourAccount.platform)">
                    {{ getPlatformDisplayName(group.ourAccount.platform) }}
                  </el-tag>
                  <el-tag
                    size="small"
                    :type="getAccountStatusTagType(group.ourAccount.status)"
                    style="margin-left: 8px"
                  >
                    {{ getAccountStatusText(group.ourAccount.status) }}
                  </el-tag>
                  <span class="benchmark-count">对标 {{ group.benchmarks.length }} 个账号</span>
                </div>
              </div>
              <div class="account-actions">
                <el-button size="small" @click="addBenchmarkForAccount(group.ourAccount)">
                  <el-icon><Plus /></el-icon>
                  添加对标
                </el-button>
                <el-button size="small" @click="toggleGroupExpand(group.ourAccount.id)">
                  <el-icon :class="group.expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'">
                    <ArrowDown v-if="!group.expanded" />
                    <ArrowUp v-else />
                  </el-icon>
                </el-button>
              </div>
            </div>
          </div>

          <!-- 对标账号列表 -->
          <div v-show="group.expanded" class="benchmark-list">
            <div v-if="group.benchmarks.length === 0" class="empty-benchmarks">
              <el-empty description="暂无对标账号" :image-size="80">
                <el-button type="primary" @click="addBenchmarkForAccount(group.ourAccount)">
                  添加第一个对标账号
                </el-button>
              </el-empty>
            </div>
            <div v-else class="benchmark-cards">
              <div
                v-for="benchmark in group.benchmarks"
                :key="benchmark.id"
                class="benchmark-card"
                :class="{ 'inactive-benchmark': benchmark.status && benchmark.status !== 'active' }"
                @contextmenu.prevent="showContextMenu($event, benchmark)"
              >
                <div class="benchmark-header">
                  <div class="benchmark-avatar">
                    <img v-if="benchmark.avatar" :src="benchmark.avatar" :alt="benchmark.account_name" />
                    <div v-else class="avatar-placeholder">
                      {{ getAccountInitial(benchmark.account_name) }}
                    </div>
                  </div>
                  <div class="benchmark-info">
                    <div class="benchmark-name-row">
                      <h4 class="benchmark-name">
                        <a :href="benchmark.account_url" target="_blank" class="benchmark-link">
                          {{ benchmark.account_name }}
                        </a>
                      </h4>
                      <div class="benchmark-name-actions">
                        <el-button size="small" text @click.stop="editBenchmark(benchmark)" class="edit-btn">
                          <el-icon><Edit /></el-icon>
                        </el-button>
                      </div>
                    </div>
                    <div class="benchmark-meta">
                      <el-tag size="small" type="info">{{ benchmark.platform }}</el-tag>
                      <el-tag
                        size="small"
                        :type="getBenchmarkTypeColor(benchmark.benchmark_type)"
                        style="margin-left: 4px"
                      >
                        {{ getBenchmarkTypeText(benchmark.benchmark_type) }}
                      </el-tag>
                      <el-tag
                        size="small"
                        :type="getStatusColor(benchmark.status)"
                        style="margin-left: 4px"
                      >
                        {{ getStatusText(benchmark.status) }}
                      </el-tag>
                      <div class="priority-stars">
                        {{ '★'.repeat(benchmark.priority || 0) }}{{ '☆'.repeat(5 - (benchmark.priority || 0)) }}
                      </div>
                    </div>
                  </div>
                  <div class="benchmark-actions">
                    <el-button size="small" @click.stop="viewBenchmarkDetail(benchmark)">
                      <el-icon><View /></el-icon>
                    </el-button>
                    <el-button size="small" type="primary" @click.stop="editBenchmark(benchmark)">
                      <el-icon><Edit /></el-icon>
                    </el-button>
                    <el-dropdown @command="(command) => handleBenchmarkAction(command, benchmark)" @click.stop>
                      <el-button size="small" text>
                        <el-icon><MoreFilled /></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item command="update-data">更新数据</el-dropdown-item>
                          <el-dropdown-item command="create-task">创建采集任务</el-dropdown-item>
                          <el-dropdown-item command="view-content">查看内容</el-dropdown-item>
                          <el-dropdown-item command="export">导出数据</el-dropdown-item>
                          <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
                
                <!-- 数据概览 -->
                <div v-if="benchmark.account_data" class="benchmark-data">
                  <div class="data-item" v-if="benchmark.account_data.followers">
                    <span class="data-label">粉丝</span>
                    <span class="data-value">{{ formatNumber(benchmark.account_data.followers) }}</span>
                  </div>
                  <div class="data-item" v-if="benchmark.account_data.posts_count">
                    <span class="data-label">作品</span>
                    <span class="data-value">{{ formatNumber(benchmark.account_data.posts_count) }}</span>
                  </div>
                  <div class="data-item" v-if="benchmark.account_data.engagement_rate">
                    <span class="data-label">互动率</span>
                    <span class="data-value">{{ (benchmark.account_data.engagement_rate * 100).toFixed(1) }}%</span>
                  </div>
                </div>

                <!-- 快速操作栏 -->
                <div class="benchmark-quick-actions">
                  <el-button size="small" @click.stop="viewBenchmarkDetail(benchmark)">
                    <el-icon><View /></el-icon>
                    详情
                  </el-button>
                  <el-button size="small" type="primary" @click.stop="editBenchmark(benchmark)">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                  <el-button size="small" @click.stop="updateBenchmarkData(benchmark)">
                    <el-icon><Refresh /></el-icon>
                    更新数据
                  </el-button>
                  <el-button size="small" type="danger" @click.stop="handleDeleteBenchmark(benchmark)">
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-else class="list-view">
        <el-table
          :data="filteredBenchmarks"
          stripe
          style="width: 100%"
          v-loading="loading"
          :empty-text="loading ? '加载中...' : '暂无对标账号'"
          @selection-change="handleSelectionChange"
          :row-class-name="getTableRowClassName"
        >
          <el-table-column type="selection" width="55" />

          <el-table-column label="对标账号信息" min-width="250">
            <template #default="{ row }">
              <div class="benchmark-info">
                <div class="benchmark-header">
                  <div class="benchmark-avatar">
                    <img v-if="row.avatar_url" :src="row.avatar_url" :alt="row.account_name" />
                    <div v-else class="avatar-placeholder">
                      {{ getAccountInitial(row.account_name) }}
                    </div>
                  </div>
                  <div class="benchmark-details">
                    <div class="benchmark-name">
                      <a :href="row.account_url" target="_blank" class="benchmark-link">
                        {{ row.account_name }}
                      </a>
                    </div>
                    <div class="benchmark-meta">
                      <el-tag size="small" type="info">{{ getPlatformDisplayName(row.platform) }}</el-tag>
                      <el-tag
                        size="small"
                        :type="getBenchmarkTypeColor(row.benchmark_type)"
                        style="margin-left: 4px"
                      >
                        {{ getBenchmarkTypeText(row.benchmark_type) }}
                      </el-tag>
                      <div class="priority-stars">
                        {{ '★'.repeat(row.priority || 0) }}{{ '☆'.repeat(5 - (row.priority || 0)) }}
                      </div>
                    </div>
                    <div v-if="row.description" class="benchmark-description">
                      {{ row.description }}
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="关联账号" min-width="180">
            <template #default="{ row }">
              <div class="our-account-info">
                <div v-if="row.ourAccount" class="account-item">
                  <div class="account-name">
                    {{ formatAccountName(row.ourAccount) }}
                  </div>
                  <div class="account-tags">
                    <el-tag size="small">{{ getPlatformDisplayName(row.ourAccount.platform) }}</el-tag>
                    <el-tag
                      size="small"
                      :type="getAccountStatusTagType(row.ourAccount.status)"
                      style="margin-left: 4px"
                    >
                      {{ getAccountStatusText(row.ourAccount.status) }}
                    </el-tag>
                  </div>
                </div>
                <div v-else-if="row.our_account_info && row.our_account_info.status !== 'not_found'" class="account-item">
                  <div class="account-name">
                    {{ formatOurAccountName(row.our_account_info) }}
                  </div>
                  <div class="account-tags">
                    <el-tag size="small">{{ getPlatformDisplayName(row.our_account_info.platform_id) }}</el-tag>
                    <el-tag
                      size="small"
                      :type="getAccountStatusTagType(row.our_account_info.status)"
                      style="margin-left: 4px"
                    >
                      {{ getAccountStatusText(row.our_account_info.status) }}
                    </el-tag>
                  </div>
                </div>
                <div v-else class="no-account">
                  <el-tag type="warning" size="small">未关联</el-tag>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="数据概览" min-width="150">
            <template #default="{ row }">
              <div v-if="hasAccountData(row)" class="data-summary">
                <div v-if="row.account_data.followers" class="data-item">
                  👥 {{ formatNumber(row.account_data.followers) }}
                </div>
                <div v-if="row.account_data.posts_count" class="data-item">
                  📝 {{ formatNumber(row.account_data.posts_count) }}
                </div>
                <div v-if="row.account_data.engagement_rate" class="data-item">
                  📈 {{ (row.account_data.engagement_rate * 100).toFixed(1) }}%
                </div>
              </div>
              <div v-else class="no-data">
                <span>暂无数据</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusColor(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="创建时间" width="120">
            <template #default="{ row }">
              <span class="time-text">{{ formatTime(row.created_at) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button size="small" @click="viewBenchmarkDetail(row)">
                  <el-icon><View /></el-icon>
                  详情
                </el-button>
                <el-button size="small" type="primary" @click="editBenchmark(row)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-dropdown @command="(command) => handleBenchmarkAction(command, row)">
                  <el-button size="small" text>
                    更多 <el-icon><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="update-data">更新数据</el-dropdown-item>
                      <el-dropdown-item command="create-task">创建采集任务</el-dropdown-item>
                      <el-dropdown-item command="view-content">查看内容</el-dropdown-item>
                      <el-dropdown-item command="export">导出数据</el-dropdown-item>
                      <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="totalCount"
            :page-sizes="[20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </div>

        <!-- 批量操作栏 -->
        <div v-if="selectedBenchmarks.length > 0" class="batch-actions">
          <div class="batch-info">
            已选择 {{ selectedBenchmarks.length }} 个对标账号
          </div>
          <div class="batch-buttons">
            <el-button @click="batchUpdateData">批量更新数据</el-button>
            <el-button @click="batchExport">批量导出</el-button>
            <el-button type="danger" @click="batchDelete">批量删除</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加对标账号对话框 -->
    <add-benchmark-dialog
      v-model:visible="addDialogVisible"
      :our-accounts="ourAccounts"
      :selected-our-account="selectedOurAccountForAdd"
      @success="handleAddSuccess"
    />

    <!-- 编辑对标账号对话框 -->
    <edit-benchmark-dialog
      v-model:visible="editDialogVisible"
      :benchmark="selectedBenchmark"
      :our-accounts="ourAccounts"
      @success="handleEditSuccess"
    />

    <!-- 对标账号详情对话框 -->
    <benchmark-detail-dialog
      v-model:visible="detailDialogVisible"
      :benchmark="selectedBenchmark"
    />

    <!-- 更新数据对话框 -->
    <update-data-dialog
      v-model:visible="updateDataDialogVisible"
      :benchmark="selectedBenchmark"
      @success="handleUpdateDataSuccess"
    />

    <!-- 批量操作对话框 -->
    <batch-operation-dialog
      v-model:visible="batchDialogVisible"
      :selected-benchmarks="selectedBenchmarks"
      :operation-type="batchOperationType"
      @success="handleBatchSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, Refresh, ArrowDown, ArrowUp, MoreFilled, View, Edit, Delete } from '@element-plus/icons-vue'
import { getBenchmarkAccounts, deleteBenchmarkAccount } from '@/api/content'
import { getAccounts } from '@/api/social'
import AddBenchmarkDialog from './components/AddBenchmarkDialog.vue'
import BenchmarkDetailDialog from './components/BenchmarkDetailDialog.vue'
import EditBenchmarkDialog from './components/EditBenchmarkDialog.vue'
import UpdateDataDialog from './components/UpdateDataDialog.vue'
import BatchOperationDialog from './components/BatchOperationDialog.vue'

// 响应式数据
const loading = ref(false)
const viewMode = ref('grouped') // 'grouped' | 'list'
const searchQuery = ref('')
const filterPlatform = ref('')
const filterType = ref('')
const showDebugInfo = ref(false) // 调试信息显示控制

// 对话框状态
const addDialogVisible = ref(false)
const editDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const updateDataDialogVisible = ref(false)
const batchDialogVisible = ref(false)
const selectedBenchmark = ref(null)
const selectedOurAccountForAdd = ref(null)
const batchOperationType = ref('')

// 分页数据
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)

// 选择数据
const selectedBenchmarks = ref([])

// 数据
const benchmarkAccounts = ref([])
const ourAccounts = ref([])
const expandedGroups = ref(new Set())

// 统计数据
const stats = computed(() => {
  // 只统计活跃状态的我们的账号
  const activeOurAccounts = ourAccounts.value.filter(acc => acc.status === 'active')
  const ourAccountsCount = activeOurAccounts.length

  // 只统计活跃状态的对标账号
  const activeBenchmarks = benchmarkAccounts.value.filter(b =>
    b.status === 'active' || !b.status // 如果没有status字段，默认为活跃
  )
  const totalBenchmarks = activeBenchmarks.length

  // 活跃关联：有活跃对标账号的活跃我们的账号数量
  const activeOurAccountIds = new Set(activeOurAccounts.map(acc => acc.id || acc._id))
  const ourAccountsWithActiveBenchmarks = new Set(
    activeBenchmarks
      .filter(b => b.our_account_id && activeOurAccountIds.has(b.our_account_id)) // 确保关联的是活跃账号
      .map(b => b.our_account_id)
  ).size

  // 平均对标数：活跃对标账号数 / 有活跃对标账号的活跃我们的账号数
  const avgBenchmarksPerAccount = ourAccountsWithActiveBenchmarks > 0
    ? (totalBenchmarks / ourAccountsWithActiveBenchmarks).toFixed(1)
    : 0

  console.log('统计数据计算:', {
    totalOurAccounts: ourAccounts.value.length,
    activeOurAccounts: ourAccountsCount,
    totalBenchmarkAccounts: benchmarkAccounts.value.length,
    activeBenchmarks: totalBenchmarks,
    ourAccountsWithActiveBenchmarks,
    avgBenchmarksPerAccount,
    activeOurAccountIds: Array.from(activeOurAccountIds),
    activeBenchmarkAccountIds: activeBenchmarks.map(b => b.our_account_id)
  })

  return {
    ourAccountsCount,
    totalBenchmarks,
    activeRelations: ourAccountsWithActiveBenchmarks,
    avgBenchmarksPerAccount
  }
})

// 分组数据
const groupedData = computed(() => {
  const groups = new Map()

  // 初始化所有我们的账号（包括非活跃的，用于显示完整信息）
  ourAccounts.value.forEach(account => {
    const accountId = account.id || account._id
    groups.set(accountId, {
      ourAccount: account,
      benchmarks: [],
      expanded: expandedGroups.value.has(accountId)
    })
  })

  // 将对标账号分组
  filteredBenchmarks.value.forEach(benchmark => {
    const group = groups.get(benchmark.our_account_id)
    if (group) {
      // 将关联账号信息添加到对标账号中
      benchmark.ourAccount = group.ourAccount
      group.benchmarks.push(benchmark)
    } else {
      // 如果找不到对应的我们的账号，创建一个占位组
      const placeholderAccount = {
        id: benchmark.our_account_id,
        name: benchmark.our_account_info?.display_name || benchmark.our_account_info?.username || '未知账号',
        platform: benchmark.our_account_info?.platform_id || 'unknown',
        status: 'not_found'
      }
      groups.set(benchmark.our_account_id, {
        ourAccount: placeholderAccount,
        benchmarks: [benchmark],
        expanded: expandedGroups.value.has(benchmark.our_account_id)
      })
    }
  })

  return Array.from(groups.values()).sort((a, b) => {
    // 有对标账号的排在前面
    if (a.benchmarks.length !== b.benchmarks.length) {
      return b.benchmarks.length - a.benchmarks.length
    }
    const nameA = a.ourAccount.name || a.ourAccount.display_name || '未知'
    const nameB = b.ourAccount.name || b.ourAccount.display_name || '未知'
    return nameA.localeCompare(nameB)
  })
})

// 过滤后的对标账号
const filteredBenchmarks = computed(() => {
  // 显示所有对标账号（包括非活跃的，用于完整显示）
  let filtered = benchmarkAccounts.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(item =>
      item.account_name.toLowerCase().includes(query) ||
      (item.ourAccount && item.ourAccount.name.toLowerCase().includes(query))
    )
  }

  if (filterPlatform.value) {
    filtered = filtered.filter(item => item.platform === filterPlatform.value)
  }

  if (filterType.value) {
    filtered = filtered.filter(item => item.benchmark_type === filterType.value)
  }

  return filtered
})

// 方法
const loadData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadBenchmarkAccounts(),
      loadOurAccounts()
    ])
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadBenchmarkAccounts = async () => {
  try {
    console.log('开始加载对标账号数据...')
    const response = await getBenchmarkAccounts({
      page: currentPage.value,
      limit: pageSize.value
    })
    console.log('对标账号API响应:', response)

    // 处理响应数据
    if (response && response.items) {
      benchmarkAccounts.value = response.items
      totalCount.value = response.total || 0
      console.log('成功加载对标账号:', benchmarkAccounts.value.length, '个，总计:', totalCount.value)
      console.log('对标账号详情:', benchmarkAccounts.value.map(b => ({
        name: b.account_name,
        our_account_id: b.our_account_id,
        platform: b.platform
      })))
    } else if (Array.isArray(response)) {
      benchmarkAccounts.value = response
      totalCount.value = response.length
      console.log('成功加载对标账号(数组格式):', benchmarkAccounts.value.length, '个')
    } else {
      benchmarkAccounts.value = []
      totalCount.value = 0
      console.warn('对标账号响应数据格式异常:', response)
    }
  } catch (error) {
    console.error('加载对标账号失败:', error)
    benchmarkAccounts.value = []
    totalCount.value = 0
    throw error
  }
}

const loadOurAccounts = async () => {
  try {
    console.log('开始加载我们的账号数据...')
    const response = await getAccounts({
      status: 'active',
      limit: 100 // 修复：使用合理的限制
    })
    console.log('我们的账号API响应:', response)

    // 处理响应数据结构 - 根据实际API响应格式
    let accounts = []

    if (response && response.data) {
      // 如果响应有data字段，可能是分页格式
      if (Array.isArray(response.data)) {
        accounts = response.data
        console.log('成功加载我们的账号(response.data数组):', accounts.length, '个')
      } else if (response.data.data && Array.isArray(response.data.data)) {
        accounts = response.data.data
        console.log('成功加载我们的账号(response.data.data数组):', accounts.length, '个')
      } else {
        console.warn('data字段格式异常:', response.data)
      }
    } else if (response && response.items && Array.isArray(response.items)) {
      accounts = response.items
      console.log('成功加载我们的账号(items字段):', accounts.length, '个')
    } else if (Array.isArray(response)) {
      accounts = response
      console.log('成功加载我们的账号(直接数组):', accounts.length, '个')
    } else {
      console.warn('我们的账号响应数据格式异常:', response)
    }

    ourAccounts.value = accounts
    console.log('最终账号列表:', ourAccounts.value.map(acc => ({
      id: acc.id || acc._id,
      name: acc.name || acc.display_name,
      platform: acc.platform,
      status: acc.status
    })))
  } catch (error) {
    console.error('加载我们的账号失败:', error)
    ourAccounts.value = []
    throw error
  }
}

const handleViewModeChange = (mode) => {
  viewMode.value = mode
  // 如果切换到分组视图，默认展开有对标账号的组
  if (mode === 'grouped') {
    expandedGroups.value.clear()
    groupedData.value.forEach(group => {
      if (group.benchmarks.length > 0) {
        expandedGroups.value.add(group.ourAccount.id)
      }
    })
  }
}

const handleSearch = () => {
  // 搜索时自动展开所有组
  if (viewMode.value === 'grouped' && searchQuery.value) {
    ourAccounts.value.forEach(account => {
      expandedGroups.value.add(account.id)
    })
  }
}

const handleFilter = () => {
  // 筛选时自动展开所有组
  if (viewMode.value === 'grouped') {
    ourAccounts.value.forEach(account => {
      expandedGroups.value.add(account.id)
    })
  }
}

const toggleGroupExpand = (accountId) => {
  if (expandedGroups.value.has(accountId)) {
    expandedGroups.value.delete(accountId)
  } else {
    expandedGroups.value.add(accountId)
  }
}

const showAddDialog = () => {
  selectedOurAccountForAdd.value = null
  addDialogVisible.value = true
}

const addBenchmarkForAccount = (ourAccount) => {
  selectedOurAccountForAdd.value = ourAccount
  addDialogVisible.value = true
}

const viewBenchmarkDetail = (benchmark) => {
  selectedBenchmark.value = benchmark
  detailDialogVisible.value = true
}

const handleBenchmarkAction = async (command, benchmark) => {
  selectedBenchmark.value = benchmark

  switch (command) {
    case 'view':
      viewBenchmarkDetail(benchmark)
      break
    case 'edit':
      editBenchmark(benchmark)
      break
    case 'update-data':
      updateBenchmarkData(benchmark)
      break
    case 'create-task':
      createCollectTask(benchmark)
      break
    case 'view-content':
      viewBenchmarkContent(benchmark)
      break
    case 'export':
      exportBenchmarkData(benchmark)
      break
    case 'delete':
      await handleDeleteBenchmark(benchmark)
      break
  }
}

const handleDeleteBenchmark = async (benchmark) => {
  try {
    await ElMessageBox.confirm(
      `确定删除对标账号 "${benchmark.account_name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 使用正确的ID字段
    const benchmarkId = benchmark._id || benchmark.id
    if (!benchmarkId) {
      throw new Error('对标账号ID不存在')
    }

    await deleteBenchmarkAccount(benchmarkId)
    ElMessage.success('删除成功')
    await loadBenchmarkAccounts()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleAddSuccess = () => {
  loadBenchmarkAccounts()
}

const editBenchmark = (benchmark) => {
  selectedBenchmark.value = benchmark
  editDialogVisible.value = true
}

const handleEditSuccess = () => {
  editDialogVisible.value = false
  loadBenchmarkAccounts()
}

const updateBenchmarkData = (benchmark) => {
  selectedBenchmark.value = benchmark
  updateDataDialogVisible.value = true
}

const handleUpdateDataSuccess = () => {
  updateDataDialogVisible.value = false
  loadBenchmarkAccounts()
}

const createCollectTask = (benchmark) => {
  ElMessage.info(`为对标账号 "${benchmark.account_name}" 创建采集任务功能开发中...`)
  // TODO: 实现创建采集任务功能
}

const viewBenchmarkContent = (benchmark) => {
  ElMessage.info(`查看对标账号 "${benchmark.account_name}" 的内容功能开发中...`)
  // TODO: 跳转到内容采集页面
}

const exportBenchmarkData = (benchmark) => {
  ElMessage.info(`导出对标账号 "${benchmark.account_name}" 的数据功能开发中...`)
  // TODO: 实现数据导出功能
}

// 分页相关方法
const handlePageChange = (page) => {
  currentPage.value = page
  loadBenchmarkAccounts()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadBenchmarkAccounts()
}

// 选择相关方法
const handleSelectionChange = (selection) => {
  selectedBenchmarks.value = selection
}

// 批量操作方法
const batchUpdateData = () => {
  if (selectedBenchmarks.value.length === 0) {
    ElMessage.warning('请先选择要操作的对标账号')
    return
  }
  batchOperationType.value = 'update-data'
  batchDialogVisible.value = true
}

const batchExport = () => {
  if (selectedBenchmarks.value.length === 0) {
    ElMessage.warning('请先选择要导出的对标账号')
    return
  }
  batchOperationType.value = 'export'
  batchDialogVisible.value = true
}

const batchDelete = async () => {
  if (selectedBenchmarks.value.length === 0) {
    ElMessage.warning('请先选择要删除的对标账号')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定删除选中的 ${selectedBenchmarks.value.length} 个对标账号吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // TODO: 实现批量删除API
    ElMessage.info('批量删除功能开发中...')

  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

const handleBatchSuccess = () => {
  batchDialogVisible.value = false
  selectedBenchmarks.value = []
  loadBenchmarkAccounts()
}

// 右键菜单
const showContextMenu = (event, benchmark) => {
  event.preventDefault()

  // 创建临时的右键菜单
  const contextMenu = document.createElement('div')
  contextMenu.className = 'context-menu'
  contextMenu.innerHTML = `
    <div class="context-menu-item" data-action="view">
      <i class="el-icon-view"></i> 查看详情
    </div>
    <div class="context-menu-item" data-action="edit">
      <i class="el-icon-edit"></i> 编辑
    </div>
    <div class="context-menu-item" data-action="update-data">
      <i class="el-icon-refresh"></i> 更新数据
    </div>
    <div class="context-menu-item" data-action="create-task">
      <i class="el-icon-video-play"></i> 创建采集任务
    </div>
    <div class="context-menu-divider"></div>
    <div class="context-menu-item danger" data-action="delete">
      <i class="el-icon-delete"></i> 删除
    </div>
  `

  contextMenu.style.position = 'fixed'
  contextMenu.style.left = event.clientX + 'px'
  contextMenu.style.top = event.clientY + 'px'
  contextMenu.style.zIndex = '9999'

  document.body.appendChild(contextMenu)

  // 添加点击事件
  contextMenu.addEventListener('click', (e) => {
    const action = e.target.closest('.context-menu-item')?.dataset.action
    if (action) {
      handleBenchmarkAction(action, benchmark)
    }
    document.body.removeChild(contextMenu)
  })

  // 点击其他地方关闭菜单
  const closeMenu = (e) => {
    if (!contextMenu.contains(e.target)) {
      document.body.removeChild(contextMenu)
      document.removeEventListener('click', closeMenu)
    }
  }

  setTimeout(() => {
    document.addEventListener('click', closeMenu)
  }, 0)
}

// 工具方法
const formatAccountName = (account) => {
  if (!account) return '未知账号'

  // 获取平台名称
  const platform = account.platform_name || account.platform || '未知平台'

  // 获取账号名称，优先使用display_name，然后是name，最后是username
  let name = account.display_name || account.name
  // 修复：检查空字符串和null/undefined
  if (!name || name.trim() === '') {
    if (account.username && account.username.trim() !== '') {
      // 如果username是邮箱格式，取@前面的部分
      name = account.username.includes('@') ? account.username.split('@')[0] : account.username
    } else {
      name = '未命名账号'
    }
  }

  return `${platform}-${name}`
}

const getAccountInitial = (name) => {
  return name ? name.charAt(0).toUpperCase() : '?'
}

const getPlatformDisplayName = (platform) => {
  const platformMap = {
    youtube: 'YouTube',
    tiktok: 'TikTok',
    instagram: 'Instagram',
    douyin: '抖音',
    weibo: '微博',
    xiaohongshu: '小红书',
    kuaishou: '快手',
    bilibili: 'B站'
  }
  return platformMap[platform] || platform
}

const getPlatformTagType = (platform) => {
  const typeMap = {
    youtube: 'danger',
    tiktok: 'warning',
    instagram: 'success',
    douyin: 'primary',
    weibo: 'info',
    xiaohongshu: 'danger',
    kuaishou: 'warning',
    bilibili: 'primary'
  }
  return typeMap[platform] || 'info'
}

const getBenchmarkTypeColor = (type) => {
  const colorMap = {
    original: 'success',
    recreate: 'warning',
    repost: 'info'
  }
  return colorMap[type] || 'info'
}

const getBenchmarkTypeText = (type) => {
  const textMap = {
    original: '原创',
    recreate: '二创',
    repost: '搬运'
  }
  return textMap[type] || type
}

const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatOurAccountName = (accountInfo) => {
  if (!accountInfo) return '未知账号'

  // 获取账号名称，优先使用display_name，然后是username的前缀部分
  let name = accountInfo.display_name
  // 修复：检查空字符串和null/undefined
  if (!name || name.trim() === '') {
    if (accountInfo.username && accountInfo.username.trim() !== '') {
      // 如果username是邮箱格式，取@前面的部分
      name = accountInfo.username.includes('@') ? accountInfo.username.split('@')[0] : accountInfo.username
    } else {
      name = '未命名账号'
    }
  }

  return name
}

const hasAccountData = (benchmark) => {
  return benchmark.account_data && (
    benchmark.account_data.followers ||
    benchmark.account_data.posts_count ||
    benchmark.account_data.engagement_rate
  )
}

const getStatusColor = (status) => {
  const colorMap = {
    active: 'success',
    inactive: 'info',
    monitoring: 'warning'
  }
  return colorMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    active: '活跃',
    inactive: '非活跃',
    monitoring: '监控中'
  }
  return textMap[status] || status
}

const formatTime = (time) => {
  if (!time) return '-'
  try {
    return new Date(time).toLocaleDateString()
  } catch {
    return time
  }
}

const getAccountStatusTagType = (status) => {
  const typeMap = {
    active: 'success',
    inactive: 'info',
    monitoring: 'warning',
    suspended: 'danger'
  }
  return typeMap[status] || 'info'
}

const getAccountStatusText = (status) => {
  const textMap = {
    active: '活跃',
    inactive: '非活跃',
    monitoring: '监控中',
    suspended: '已暂停'
  }
  return textMap[status] || status || '未知'
}

const getTableRowClassName = ({ row }) => {
  // 为非活跃的对标账号添加特殊样式类
  if (row.status && row.status !== 'active') {
    return 'inactive-row'
  }
  // 为关联到非活跃我们的账号的对标账号添加特殊样式类
  if (row.ourAccount && row.ourAccount.status !== 'active') {
    return 'inactive-our-account-row'
  }
  return ''
}

// 生命周期
onMounted(() => {
  loadData()
})

// 监听视图模式变化，默认展开有对标账号的组
watch(viewMode, (newMode) => {
  if (newMode === 'grouped') {
    expandedGroups.value.clear()
    setTimeout(() => {
      groupedData.value.forEach(group => {
        if (group.benchmarks.length > 0) {
          expandedGroups.value.add(group.ourAccount.id)
        }
      })
    }, 100)
  }
})
</script>

<style scoped>
.benchmark-accounts-new {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.management-header {
  text-align: center;
  margin-bottom: 30px;
}

.management-header h1 {
  font-size: 28px;
  color: #2c3e50;
  margin-bottom: 10px;
}

.header-description {
  color: #7f8c8d;
  font-size: 16px;
  margin: 0 0 15px 0;
}

.status-notice {
  max-width: 600px;
  margin: 0 auto;
}

.debug-info {
  margin-bottom: 20px;
}

.debug-content {
  max-height: 300px;
  overflow-y: auto;
}

.debug-section {
  margin-bottom: 15px;
}

.debug-section h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 14px;
}

.debug-list {
  max-height: 100px;
  overflow-y: auto;
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
}

.debug-item {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
  font-family: monospace;
  display: flex;
  align-items: center;
  gap: 8px;
}

.debug-item.inactive {
  opacity: 0.6;
}

.status-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
  min-width: 50px;
  text-align: center;
}

.status-badge.active {
  background-color: #67c23a;
  color: white;
}

.status-badge.inactive {
  background-color: #909399;
  color: white;
}

.status-badge.monitoring {
  background-color: #e6a23c;
  color: white;
}

.status-badge.unknown {
  background-color: #f56c6c;
  color: white;
}

.stats-cards {
  margin-bottom: 30px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stat-icon {
  font-size: 32px;
  margin-right: 15px;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
  margin-top: 5px;
}

.stat-detail {
  font-size: 12px;
  color: #95a5a6;
  margin-top: 2px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.view-mode-switch {
  margin-right: 15px;
}

.main-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 分组视图样式 */
.grouped-view {
  padding: 20px;
}

.account-group {
  margin-bottom: 30px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  transition: opacity 0.2s;
}

.account-group.inactive-account {
  opacity: 0.7;
  border-color: #d3d4d6;
}

.account-group.inactive-account .our-account-card {
  background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
}

.our-account-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
}

.account-header {
  display: flex;
  align-items: center;
}

.account-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 15px;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.account-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  font-size: 20px;
  font-weight: bold;
}

.account-info {
  flex: 1;
}

.account-name {
  font-size: 20px;
  font-weight: bold;
  margin: 0 0 8px 0;
}

.account-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

.benchmark-count {
  font-size: 14px;
  opacity: 0.9;
}

.account-actions {
  display: flex;
  gap: 10px;
}

.benchmark-list {
  padding: 20px;
  background: #fafbfc;
}

.empty-benchmarks {
  text-align: center;
  padding: 40px 20px;
}

.benchmark-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.benchmark-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  position: relative;
  transition: all 0.2s;
  overflow: hidden;
}

.benchmark-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.benchmark-card:hover .benchmark-quick-actions {
  opacity: 1;
  transform: translateY(0);
}

.benchmark-card.inactive-benchmark {
  opacity: 0.6;
  border-color: #d3d4d6;
}

.benchmark-card.inactive-benchmark:hover {
  opacity: 0.8;
}

.benchmark-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
}

.benchmark-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 12px;
  overflow: hidden;
  flex-shrink: 0;
}

.benchmark-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.benchmark-avatar .avatar-placeholder {
  background: #f0f2f5;
  color: #666;
  font-size: 16px;
}

.benchmark-info {
  flex: 1;
  min-width: 0;
}

.benchmark-name-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 5px;
}

.benchmark-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  flex: 1;
}

.benchmark-name-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.benchmark-card:hover .benchmark-name-actions {
  opacity: 1;
}

.edit-btn {
  color: #409eff;
  padding: 4px;
}

.edit-btn:hover {
  background-color: rgba(64, 158, 255, 0.1);
}

.benchmark-link {
  color: #2c3e50;
  text-decoration: none;
  word-break: break-all;
}

.benchmark-link:hover {
  color: #409eff;
}

.benchmark-meta {
  display: flex;
  align-items: center;
  gap: 5px;
  flex-wrap: wrap;
}

.priority-stars {
  color: #f39c12;
  font-size: 12px;
  margin-left: 5px;
}

.benchmark-actions {
  margin-left: 10px;
}

.benchmark-data {
  display: flex;
  gap: 15px;
  padding-top: 10px;
  border-top: 1px solid #f0f2f5;
}

.data-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 12px;
}

.data-label {
  color: #909399;
  margin-bottom: 2px;
}

.data-value {
  font-weight: 600;
  color: #2c3e50;
}

.benchmark-quick-actions {
  display: flex;
  gap: 8px;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f2f5;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.2s ease;
}

.benchmark-quick-actions .el-button {
  flex: 1;
  font-size: 12px;
}

.benchmark-quick-actions .el-button .el-icon {
  margin-right: 4px;
}

/* 列表视图样式 */
.list-view {
  padding: 20px;
}

.benchmark-info {
  width: 100%;
}

.benchmark-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.benchmark-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  border: 1px solid #e4e7ed;
}

.benchmark-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.benchmark-avatar .avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f2f5;
  color: #666;
  font-size: 16px;
  font-weight: bold;
}

.benchmark-details {
  flex: 1;
  min-width: 0;
}

.benchmark-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
}

.benchmark-link {
  color: #2c3e50;
  text-decoration: none;
  word-break: break-all;
}

.benchmark-link:hover {
  color: #409eff;
}

.benchmark-meta {
  display: flex;
  align-items: center;
  gap: 5px;
  flex-wrap: wrap;
  margin-bottom: 5px;
}

.benchmark-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin-top: 5px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.our-account-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.account-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.account-name {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.account-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.no-account {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
}

.data-summary {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.data-item {
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 4px;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  color: #909399;
  font-size: 12px;
}

.time-text {
  font-size: 12px;
  color: #909399;
}

.action-buttons {
  display: flex;
  gap: 5px;
  align-items: center;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 15px 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  z-index: 1000;
}

.batch-info {
  font-size: 14px;
  color: #606266;
}

.batch-buttons {
  display: flex;
  gap: 10px;
}

/* 表格行状态样式 */
:deep(.el-table .inactive-row) {
  opacity: 0.6;
  background-color: #f8f9fa !important;
}

:deep(.el-table .inactive-our-account-row) {
  opacity: 0.7;
  background-color: #fdf6ec !important;
}

:deep(.el-table .inactive-row:hover) {
  opacity: 0.8 !important;
}

:deep(.el-table .inactive-our-account-row:hover) {
  opacity: 0.9 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .benchmark-accounts-new {
    padding: 10px;
  }

  .toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .toolbar-left {
    flex-wrap: wrap;
    gap: 10px;
  }

  .benchmark-cards {
    grid-template-columns: 1fr;
  }

  .account-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .account-actions {
    align-self: stretch;
  }
}

/* 右键菜单样式 */
.context-menu {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 4px 0;
  min-width: 150px;
  font-size: 14px;
}

.context-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.context-menu-item:hover {
  background-color: #f5f7fa;
}

.context-menu-item.danger {
  color: #f56c6c;
}

.context-menu-item.danger:hover {
  background-color: #fef0f0;
}

.context-menu-divider {
  height: 1px;
  background-color: #e4e7ed;
  margin: 4px 0;
}
</style>
