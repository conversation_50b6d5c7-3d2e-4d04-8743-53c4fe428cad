import { request } from '@/utils/request'

// 类型定义
export interface GenerationTask {
  id: string
  title: string
  type: 'text' | 'storyboard' | 'image' | 'video' | 'full_pipeline'
  description: string
  style?: string
  count: number
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'paused'
  progress: number
  current_step?: string
  created_at: string
  completed_at?: string
  results?: GenerationResult[]
  config?: GenerationConfig
}

export interface GenerationResult {
  id: string
  type: 'text' | 'image' | 'video'
  title: string
  content?: string
  preview_url?: string
  download_url?: string
  metadata?: Record<string, any>
}

export interface GenerationConfig {
  comfyui_workflow?: string
  model_settings?: Record<string, any>
  output_settings?: Record<string, any>
}

export interface CreateTaskRequest {
  title: string
  type: string
  description: string
  style?: string
  count: number
  config?: GenerationConfig
}

export interface TaskListResponse {
  tasks: GenerationTask[]
  total: number
  page: number
  page_size: number
}

export interface TaskProgressResponse {
  task_id: string
  status: string
  progress: number
  current_step?: string
  error_message?: string
}

// API接口

/**
 * 获取生成任务列表
 */
export const getGenerationTasks = (params?: {
  page?: number
  page_size?: number
  status?: string
  type?: string
}) => {
  return request<TaskListResponse>({
    url: '/api/v1/content-generate/tasks',
    method: 'get',
    params
  })
}

/**
 * 创建生成任务
 */
export const createGenerationTask = (data: CreateTaskRequest) => {
  return request<GenerationTask>({
    url: '/api/v1/content-generate/tasks',
    method: 'post',
    data
  })
}

/**
 * 获取任务详情
 */
export const getGenerationTask = (taskId: string) => {
  return request<GenerationTask>({
    url: `/api/v1/content-generate/tasks/${taskId}`,
    method: 'get'
  })
}

/**
 * 更新任务
 */
export const updateGenerationTask = (taskId: string, data: Partial<GenerationTask>) => {
  return request<GenerationTask>({
    url: `/api/v1/content-generate/tasks/${taskId}`,
    method: 'put',
    data
  })
}

/**
 * 删除任务
 */
export const deleteGenerationTask = (taskId: string) => {
  return request({
    url: `/api/v1/content-generate/tasks/${taskId}`,
    method: 'delete'
  })
}

/**
 * 开始生成任务
 */
export const startGenerationTask = (taskId: string) => {
  return request({
    url: `/api/v1/content-generate/tasks/${taskId}/start`,
    method: 'post'
  })
}

/**
 * 暂停生成任务
 */
export const pauseGenerationTask = (taskId: string) => {
  return request({
    url: `/api/v1/content-generate/tasks/${taskId}/pause`,
    method: 'post'
  })
}

/**
 * 停止生成任务
 */
export const stopGenerationTask = (taskId: string) => {
  return request({
    url: `/api/v1/content-generate/tasks/${taskId}/stop`,
    method: 'post'
  })
}

/**
 * 获取任务进度
 */
export const getTaskProgress = (taskId: string) => {
  return request<TaskProgressResponse>({
    url: `/api/v1/content-generate/tasks/${taskId}/progress`,
    method: 'get'
  })
}

/**
 * 获取任务结果
 */
export const getTaskResults = (taskId: string) => {
  return request<GenerationResult[]>({
    url: `/api/v1/content-generate/tasks/${taskId}/results`,
    method: 'get'
  })
}

/**
 * 下载生成结果
 */
export const downloadResult = (resultId: string) => {
  return request({
    url: `/api/v1/content-generate/results/${resultId}/download`,
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 获取可用的生成模型列表
 */
export const getAvailableModels = () => {
  return request<{
    text_models: string[]
    image_models: string[]
    video_models: string[]
  }>({
    url: '/api/v1/content-generate/models',
    method: 'get'
  })
}

/**
 * 获取ComfyUI工作流模板
 */
export const getComfyUIWorkflows = () => {
  return request<{
    workflows: Array<{
      id: string
      name: string
      description: string
      type: string
      template: Record<string, any>
    }>
  }>({
    url: '/api/v1/content-generate/comfyui/workflows',
    method: 'get'
  })
}

/**
 * 测试ComfyUI连接
 */
export const testComfyUIConnection = () => {
  return request<{
    status: 'connected' | 'disconnected'
    version?: string
    available_models?: string[]
  }>({
    url: '/api/v1/content-generate/comfyui/test',
    method: 'get'
  })
}

/**
 * 获取生成统计信息
 */
export const getGenerationStats = () => {
  return request<{
    total_tasks: number
    completed_tasks: number
    failed_tasks: number
    processing_tasks: number
    total_results: number
  }>({
    url: '/api/v1/content-generate/stats',
    method: 'get'
  })
}
